#!/bin/bash

# 启动多个并发Celery Worker以提高爬取速度
echo "🚀 启动高性能Celery Worker集群..."

# 切换到项目根目录
cd "$(dirname "$0")"

# 再切换到后端目录
cd backend

# 激活虚拟环境
source venv/bin/activate

# 设置Python路径，确保可以导入config模块
export PYTHONPATH="$(pwd):$PYTHONPATH"

# 杀死现有的worker进程
echo "🔄 停止现有Worker进程..."
pkill -f "celery.*worker" || true
sleep 2

# 启动4个并发Worker，每个Worker使用2个进程
echo "🎯 启动Worker-1 (concurrency=2)..."
nohup celery -A app.celery_app.celery worker --loglevel=info --concurrency=2 --pool=prefork -n worker1@%h > ../logs/worker1.log 2>&1 &

echo "🎯 启动Worker-2 (concurrency=2)..."
nohup celery -A app.celery_app.celery worker --loglevel=info --concurrency=2 --pool=prefork -n worker2@%h > ../logs/worker2.log 2>&1 &

echo "🎯 启动Worker-3 (concurrency=2)..."
nohup celery -A app.celery_app.celery worker --loglevel=info --concurrency=2 --pool=prefork -n worker3@%h > ../logs/worker3.log 2>&1 &

echo "🎯 启动Worker-4 (concurrency=2)..."
nohup celery -A app.celery_app.celery worker --loglevel=info --concurrency=2 --pool=prefork -n worker4@%h > ../logs/worker4.log 2>&1 &

sleep 3

# 检查Worker状态
echo "📊 检查Worker状态..."
celery -A app.celery_app.celery inspect active_queues 2>/dev/null || echo "Worker正在启动中..."

echo "✅ Worker集群启动完成！总并发能力: 8个进程"

echo ""
echo "📝 日志文件位置:"
echo "   Worker-1: logs/worker1.log"
echo "   Worker-2: logs/worker2.log" 
echo "   Worker-3: logs/worker3.log"
echo "   Worker-4: logs/worker4.log"
echo ""
echo "🔧 停止所有Worker: pkill -f 'celery.*worker'"
echo "🔍 查看Worker进程: ps aux | grep celery"