import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, message, Layout } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import './App.css';
import MainLayout from './components/MainLayout';
import UrlManagement from './pages/UrlManagement';
import CrawlerTasks from './pages/CrawlerTasks';
import CrawlResults from './pages/CrawlResults';
import DetectionResults from './pages/DetectionResults';
import ExportManagement from './pages/ExportManagement';
import SystemSettings from './pages/SystemSettings';
import { ApiService } from './services/api';

const { Content } = Layout;

interface LoginForm {
  username: string;
  password: string;
}

function App() {
  const [loading, setLoading] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [username, setUsername] = useState('admin');

  // 检查登录状态
  useEffect(() => {
    const token = localStorage.getItem('access_token');
    if (token) {
      // 验证token有效性
      ApiService.getProfile()
        .then(response => {
          setIsLoggedIn(true);
          setUsername(response.user?.username || 'admin');
        })
        .catch(() => {
          // token无效，清除
          localStorage.removeItem('access_token');
          setIsLoggedIn(false);
        });
    }
  }, []);

  const handleLogin = async (values: LoginForm) => {
    if (loading) return;
    
    setLoading(true);
    try {
      console.log('🚀 发送登录请求:', values);
      
      const response = await ApiService.login(values);
      console.log('✅ 登录响应:', response);
      
      localStorage.setItem('access_token', response.access_token);
      message.success('登录成功！');
      setIsLoggedIn(true);
      setUsername(values.username);
    } catch (error: any) {
      console.error('❌ 登录错误:', error);
      const errorMsg = error.response?.data?.error || '登录失败';
      message.error(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('access_token');
    setIsLoggedIn(false);
    setUsername('');
    message.info('已退出登录');
  };

  // 已登录，显示主应用界面
  if (isLoggedIn) {
    return (
      <Router>
        <Routes>
          <Route path="/" element={<MainLayout username={username} onLogout={handleLogout} />}>
            <Route index element={<Navigate to="/urls" replace />} />
            <Route path="/urls" element={<UrlManagement />} />
            <Route path="/crawler" element={<CrawlerTasks />} />
            <Route path="/results" element={<CrawlResults />} />
            <Route path="/detection" element={<DetectionResults />} />
            <Route path="/export" element={<ExportManagement />} />
            <Route path="/settings" element={<SystemSettings />} />
            <Route path="/profile" element={<div style={{ padding: 24 }}>个人信息页面（开发中）</div>} />
          </Route>
        </Routes>
      </Router>
    );
  }

  // 未登录，显示登录页面
  return (
    <Layout style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
      <Content style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Card
          title={<h2 style={{ textAlign: 'center', color: '#1890ff', margin: 0 }}>内容监测系统</h2>}
          style={{ width: 400, boxShadow: '0 4px 8px rgba(0,0,0,0.1)' }}
        >
          <Form
            name="login"
            onFinish={handleLogin}
            initialValues={{
              username: 'admin',
              password: 'Fuition@2025'
            }}
            layout="vertical"
            size="large"
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入用户名' }]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="用户名"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                style={{ marginBottom: '8px' }}
              >
                登录
              </Button>
              <Button
                block
                onClick={() => {
                  handleLogin({
                    username: 'admin',
                    password: 'Fuition@2025'
                  });
                }}
                loading={loading}
              >
                快速登录 (admin)
              </Button>
            </Form.Item>
          </Form>

          <div style={{ marginTop: 16, padding: '12px', background: '#f5f5f5', borderRadius: 4, fontSize: '12px', color: '#666' }}>
            <strong>系统功能：</strong>
            <ul style={{ margin: '4px 0', paddingLeft: 16 }}>
              <li>URL批量管理和Excel导入</li>
              <li>多线程网页爬虫任务</li>
              <li>智能内容检测（关键词+API）</li>
              <li>检测结果导出（Excel/HTML）</li>
              <li>系统配置和性能监控</li>
            </ul>
          </div>
        </Card>
      </Content>
    </Layout>
  );
}

export default App;
