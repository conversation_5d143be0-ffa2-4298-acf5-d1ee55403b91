import axios, { AxiosResponse } from 'axios';

// 配置API基础URL
const API_BASE_URL = 'http://localhost:5001/api';

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理通用错误
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('access_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 类型定义
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  user: {
    id: number;
    username: string;
    email: string;
  };
}

export interface Website {
  id: number;
  url: string;
  domain: string;
  is_active: boolean;
  created_at: string;
  batch_id?: string;
}

export interface CrawlTask {
  id: number;
  task_id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'paused' | 'completed' | 'stopped' | 'failed';
  progress: number;
  max_threads: number;
  max_depth: number;
  request_delay: number;
  enable_js: boolean;
  total_urls: number;
  crawled_urls: number;
  failed_urls: number;
  user_id: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
}

export interface DetectionResult {
  id: number;
  content_type: string;
  content_snippet: string;
  detection_method: string;
  is_nsfw: boolean;
  confidence_score: number;
  detected_keywords?: string;
  keyword_categories?: string;
  detected_at: string;
  crawl_result_id: number;
}

export interface Keyword {
  id: number;
  word: string;
  category: string;
  description?: string;
  is_active: boolean;
  is_regex: boolean;
  case_sensitive: boolean;
  hit_count: number;
  created_at: string;
}

export interface CrawlResult {
  id: number;
  url: string;
  title?: string;
  content_text?: string;
  content_html?: string;
  status: string;
  response_code?: number;
  content_type?: string;
  content_length?: number;
  images_count?: number;
  links_count?: number;
  page_load_time?: number;
  is_nsfw?: boolean;
  error_message?: string;
  crawled_at?: string;
  depth?: number;
  parent_url?: string;
  task_name?: string;
}

export interface PageImage {
  id: number;
  url: string;
  alt_text?: string;
  width?: number;
  height?: number;
  file_size?: number;
  format?: string;
  is_valid?: boolean;
}

export interface CrawlResultDetail extends CrawlResult {
  images: PageImage[];
  detection_results: DetectionResult[];
}

// API服务类
export class ApiService {
  // 认证相关
  static async login(data: LoginRequest): Promise<LoginResponse> {
    const response: AxiosResponse<LoginResponse> = await apiClient.post('/auth/login', data);
    return response.data;
  }

  static async getProfile() {
    const response = await apiClient.get('/auth/profile');
    return response.data;
  }

  // URL管理相关
  static async getUrls(page = 1, perPage = 20, domain = '', getAll = false) {
    const response = await apiClient.get('/urls/', {
      params: { page, per_page: perPage, domain, all: getAll }
    });
    return response.data;
  }

  static async addUrl(url: string) {
    const response = await apiClient.post('/urls/add', { url });
    return response.data;
  }

  static async uploadUrls(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    const response = await apiClient.post('/urls/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  static async deleteUrl(urlId: number) {
    const response = await apiClient.delete(`/urls/${urlId}`);
    return response.data;
  }

  static async getBatches() {
    const response = await apiClient.get('/urls/batches');
    return response.data;
  }

  // 爬虫任务相关
  static async getCrawlTasks(page = 1, pageSize = 20, status = '') {
    const response = await apiClient.get('/crawler/tasks', {
      params: { page, per_page: pageSize, status }
    });
    return response.data;
  }

  static async createCrawlTask(data: {
    name: string;
    description?: string;
    max_threads: number;
    max_depth: number;
    request_delay: number;
    enable_js: boolean;
    same_domain_only?: boolean;
    lightweight_mode?: boolean;
    selected_urls: number[];
  }) {
    const response = await apiClient.post('/crawler/tasks', data);
    return response.data;
  }

  static async getCrawlTask(taskId: string) {
    const response = await apiClient.get(`/crawler/tasks/${taskId}`);
    return response.data;
  }

  static async getCrawlTaskDetail(taskId: string) {
    const response = await apiClient.get(`/crawler/tasks/${taskId}`);
    return response.data.task;
  }

  static async startCrawlTask(taskId: string) {
    const response = await apiClient.post(`/crawler/tasks/${taskId}/start`);
    return response.data;
  }

  static async pauseCrawlTask(taskId: string) {
    const response = await apiClient.post(`/crawler/tasks/${taskId}/pause`);
    return response.data;
  }

  static async stopCrawlTask(taskId: string) {
    const response = await apiClient.post(`/crawler/tasks/${taskId}/stop`);
    return response.data;
  }

  // 删除爬虫任务
  static async deleteCrawlTask(taskId: string, force = false): Promise<any> {
    const params = force ? '?force=true' : '';
    const response = await apiClient.delete(`/crawler/tasks/${taskId}${params}`);
    return response.data;
  }

  // 强制停止任务
  static async forceStopTask(taskId: string): Promise<any> {
    const response = await apiClient.post(`/crawler/tasks/${taskId}/force-stop`);
    return response.data;
  }

  // 清理异常任务
  static async cleanupTasks(): Promise<any> {
    const response = await apiClient.post('/crawler/tasks/cleanup');
    return response.data;
  }

  // 检测结果相关
  static async getDetectionResults(page = 1, perPage = 20, isNsfw?: boolean) {
    const response = await apiClient.get('/detection/results', {
      params: { page, per_page: perPage, is_nsfw: isNsfw }
    });
    return response.data;
  }

  static async detectContent(crawlResultId: number) {
    const response = await apiClient.post(`/detection/detect/${crawlResultId}`);
    return response.data;
  }

  // 关键词管理
  static async getKeywords(page = 1, perPage = 50, category = '') {
    const response = await apiClient.get('/detection/keywords', {
      params: { page, per_page: perPage, category }
    });
    return response.data;
  }

  static async addKeyword(data: { keyword: string; category: string }) {
    const response = await apiClient.post('/detection/keywords', data);
    return response.data;
  }

  static async getDetectionConfig() {
    const response = await apiClient.get('/detection/config');
    return response.data;
  }

  // 导出相关
  static async getExportSummary() {
    const response = await apiClient.get('/export/summary');
    return response.data;
  }

  static async exportToExcel(filters?: {
    is_nsfw?: boolean;
    start_date?: string;
    end_date?: string;
  }) {
    const response = await apiClient.post('/export/excel', filters, {
      responseType: 'blob',
    });
    return response;
  }

  static async exportToHtml(filters?: { is_nsfw?: boolean }) {
    const response = await apiClient.post('/export/html', filters, {
      responseType: 'blob',
    });
    return response;
  }

  // 健康检查
  static async healthCheck() {
    const response = await apiClient.get('/health');
    return response.data;
  }

  // 爬取结果相关
  static async getCrawlResults(page = 1, perPage = 20, taskId?: string) {
    const response = await apiClient.get('/crawler/results', {
      params: { page, per_page: perPage, task_id: taskId }
    });
    return response.data;
  }

  static async getCrawlResultDetail(resultId: number) {
    const response = await apiClient.get(`/crawler/results/${resultId}`);
    return response.data;
  }

  static async getCrawlResultImages(resultId: number) {
    const response = await apiClient.get(`/crawler/results/${resultId}/images`);
    return response.data;
  }
}

export default ApiService; 