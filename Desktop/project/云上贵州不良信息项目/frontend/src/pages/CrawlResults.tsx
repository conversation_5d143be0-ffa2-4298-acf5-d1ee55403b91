import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Modal, Tag, Image, Tabs, Input, Select, message, Tooltip, Typography } from 'antd';
import { EyeOutlined, ReloadOutlined, SearchOutlined, FileImageOutlined, FileTextOutlined, LinkOutlined } from '@ant-design/icons';
import type { TableProps } from 'antd';
import { ApiService, CrawlResult, CrawlResultDetail, PageImage } from '../services/api';

const { Option } = Select;
const { Search } = Input;
const { Text, Paragraph } = Typography;

const CrawlResults: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<CrawlResult[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [imagesModalVisible, setImagesModalVisible] = useState(false);
  const [selectedResult, setSelectedResult] = useState<CrawlResultDetail | null>(null);
  const [selectedImages, setSelectedImages] = useState<PageImage[]>([]);
  const [filters, setFilters] = useState<{
    taskId?: string;
    status?: string;
    searchUrl?: string;
  }>({});

  // 加载爬取结果
  const loadResults = async (page = 1, pageSize = 20) => {
    setLoading(true);
    try {
      const response = await ApiService.getCrawlResults(page, pageSize, filters.taskId);
      setResults(response.results || []);
      setPagination({
        current: page,
        pageSize,
        total: response.total || 0,
      });
    } catch (error: any) {
      message.error('获取爬取结果失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 查看详情
  const viewDetail = async (record: CrawlResult) => {
    setLoading(true);
    try {
      const detail = await ApiService.getCrawlResultDetail(record.id);
      setSelectedResult(detail);
      setDetailModalVisible(true);
    } catch (error: any) {
      message.error('获取详情失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 查看图片
  const viewImages = async (record: CrawlResult) => {
    setLoading(true);
    try {
      const response = await ApiService.getCrawlResultImages(record.id);
      setSelectedImages(response.images || []);
      setImagesModalVisible(true);
    } catch (error: any) {
      message.error('获取图片失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadResults();
  }, [filters]);

  const columns: TableProps<CrawlResult>['columns'] = [
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      ellipsis: true,
      width: 250,
      render: (url: string) => (
        <Tooltip title={url}>
          <a href={url} target="_blank" rel="noopener noreferrer">
            <LinkOutlined /> {url}
          </a>
        </Tooltip>
      ),
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      width: 200,
      render: (title: string) => title || '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const color = status === 'completed' ? 'green' : status === 'failed' ? 'red' : 'orange';
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: '响应码',
      dataIndex: 'response_code',
      key: 'response_code',
      width: 80,
      render: (code: number) => {
        if (!code) return '-';
        const color = code >= 200 && code < 300 ? 'green' : code >= 400 ? 'red' : 'orange';
        return <Tag color={color}>{code}</Tag>;
      },
    },
    {
      title: '内容长度',
      dataIndex: 'content_length',
      key: 'content_length',
      width: 100,
      render: (length: number) => length ? `${(length / 1024).toFixed(1)}KB` : '-',
    },
    {
      title: '图片数',
      dataIndex: 'images_count',
      key: 'images_count',
      width: 80,
      render: (count: number, record) => (
        <Space>
          <span>{count || 0}</span>
          {count > 0 && (
            <Button 
              type="link" 
              size="small" 
              icon={<FileImageOutlined />}
              onClick={() => viewImages(record)}
            >
              查看
            </Button>
          )}
        </Space>
      ),
    },
    {
      title: 'NSFW',
      dataIndex: 'is_nsfw',
      key: 'is_nsfw',
      width: 80,
      render: (isNsfw: boolean) => (
        <Tag color={isNsfw ? 'red' : 'green'}>
          {isNsfw ? '是' : '否'}
        </Tag>
      ),
    },
    {
      title: '爬取时间',
      dataIndex: 'crawled_at',
      key: 'crawled_at',
      width: 150,
      render: (date: string) => date ? new Date(date).toLocaleString('zh-CN') : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => viewDetail(record)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card title="爬取结果管理">
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Search
              placeholder="搜索URL"
              style={{ width: 200 }}
              onSearch={(value) => setFilters({ ...filters, searchUrl: value })}
              allowClear
            />
            <Select
              placeholder="筛选状态"
              style={{ width: 120 }}
              allowClear
              onChange={(value) => setFilters({ ...filters, status: value })}
            >
              <Option value="completed">成功</Option>
              <Option value="failed">失败</Option>
              <Option value="pending">待处理</Option>
            </Select>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={() => loadResults(pagination.current, pagination.pageSize)}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </div>

        <Table<CrawlResult>
          dataSource={results}
          columns={columns}
          loading={loading}
          rowKey="id"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              setPagination({ ...pagination, current: page, pageSize });
              loadResults(page, pageSize);
            },
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title="爬取结果详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={1000}
        destroyOnClose
      >
        {selectedResult && (
          <div>
            <Tabs
              items={[
                {
                  key: 'content',
                  label: `文本内容 (${selectedResult.content_text?.length || 0} 字符)`,
                  children: (
                    <div>
                      <div style={{ marginBottom: 16 }}>
                        <Space wrap>
                          <Tag>URL: {selectedResult.url}</Tag>
                          <Tag>标题: {selectedResult.title || '无标题'}</Tag>
                          <Tag color={selectedResult.status === 'completed' ? 'green' : 'red'}>
                            状态: {selectedResult.status}
                          </Tag>
                          <Tag>响应码: {selectedResult.response_code}</Tag>
                        </Space>
                      </div>
                      <div style={{ 
                        border: '1px solid #d9d9d9', 
                        borderRadius: 4, 
                        padding: 16, 
                        backgroundColor: '#fafafa',
                        maxHeight: 400,
                        overflow: 'auto'
                      }}>
                        <Paragraph copyable={{ text: selectedResult.content_text }}>
                          <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                            {selectedResult.content_text || '无文本内容'}
                          </pre>
                        </Paragraph>
                      </div>
                    </div>
                  ),
                },
                {
                  key: 'images',
                  label: `图片 (${selectedResult.images?.length || 0} 张)`,
                  children: (
                    <div>
                      {selectedResult.images && selectedResult.images.length > 0 ? (
                        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))', gap: 16 }}>
                          {selectedResult.images.map(img => (
                            <Card
                              key={img.id}
                              size="small"
                              cover={
                                <Image
                                  src={img.url}
                                  alt={img.alt_text || '图片'}
                                  style={{ height: 150, objectFit: 'cover' }}
                                  fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                                />
                              }
                            >
                              <Card.Meta
                                title={img.alt_text || '无描述'}
                                description={
                                  <div>
                                    <Text type="secondary" style={{ fontSize: 12 }}>
                                      {img.format} • {img.width}×{img.height}
                                    </Text>
                                  </div>
                                }
                              />
                            </Card>
                          ))}
                        </div>
                      ) : (
                        <div style={{ textAlign: 'center', padding: 40, color: '#999' }}>
                          <FileImageOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                          <div>该页面没有图片</div>
                        </div>
                      )}
                    </div>
                  ),
                },
                {
                  key: 'detection',
                  label: `检测结果 (${selectedResult.detection_results?.length || 0} 项)`,
                  children: (
                    <div>
                      {selectedResult.detection_results && selectedResult.detection_results.length > 0 ? (
                        selectedResult.detection_results.map((dr, index) => (
                          <Card key={dr.id} size="small" style={{ marginBottom: 8 }}>
                            <Space direction="vertical" style={{ width: '100%' }}>
                              <Space wrap>
                                <Tag color={dr.is_nsfw ? 'red' : 'green'}>
                                  {dr.is_nsfw ? 'NSFW' : '正常'}
                                </Tag>
                                <Tag color={dr.detection_method === 'keyword' ? 'orange' : 'purple'}>
                                  {dr.detection_method === 'keyword' ? '关键词检测' : 'API检测'}
                                </Tag>
                                <Tag>置信度: {Math.round((dr.confidence_score || 0) * 100)}%</Tag>
                              </Space>
                              {dr.detected_keywords && (
                                <div>
                                  <Text strong>检测到的关键词: </Text>
                                  <Text code>{dr.detected_keywords}</Text>
                                </div>
                              )}
                              {dr.content_snippet && (
                                <div>
                                  <Text strong>内容片段: </Text>
                                  <Text style={{ fontSize: 12 }}>{dr.content_snippet}</Text>
                                </div>
                              )}
                            </Space>
                          </Card>
                        ))
                      ) : (
                        <div style={{ textAlign: 'center', padding: 40, color: '#999' }}>
                          <SearchOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                          <div>该页面没有检测结果</div>
                        </div>
                      )}
                    </div>
                  ),
                },
              ]}
            />
          </div>
        )}
      </Modal>

      {/* 图片查看模态框 */}
      <Modal
        title="页面图片"
        open={imagesModalVisible}
        onCancel={() => setImagesModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setImagesModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
        destroyOnClose
      >
        {selectedImages.length > 0 ? (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(180px, 1fr))', gap: 12 }}>
            {selectedImages.map(img => (
              <Card
                key={img.id}
                size="small"
                bodyStyle={{ padding: 8 }}
                cover={
                  <Image
                    src={img.url}
                    alt={img.alt_text || '图片'}
                    style={{ height: 120, objectFit: 'cover' }}
                    fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                  />
                }
              >
                <div style={{ fontSize: 11, color: '#666' }}>
                  {img.format} • {img.width}×{img.height}
                  {img.alt_text && <div>{img.alt_text}</div>}
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: 40, color: '#999' }}>
            <FileImageOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <div>没有找到图片</div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CrawlResults;