import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Modal, Form, Input, InputNumber, Select, message, Tag, Progress, Popconfirm, Transfer, Drawer, List, Typography } from 'antd';
import { PlusOutlined, PlayCircleOutlined, PauseOutlined, StopOutlined, ReloadOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import type { TableProps } from 'antd';
import { ApiService, CrawlTask, Website } from '../services/api';

const { Option } = Select;
const { Text } = Typography;

const CrawlerTasks: React.FC = () => {
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [tasks, setTasks] = useState<CrawlTask[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [form] = Form.useForm();
  
  // URL选择相关状态
  const [availableUrls, setAvailableUrls] = useState<Website[]>([]);
  const [selectedUrlIds, setSelectedUrlIds] = useState<string[]>([]);
  const [urlsLoading, setUrlsLoading] = useState(false);

  // 任务详情相关状态
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [currentTaskDetail, setCurrentTaskDetail] = useState<any>(null);
  const [detailLoading, setDetailLoading] = useState(false);

  // 加载任务列表
  const loadTasks = async (page = 1, pageSize = 20, status = '') => {
    setLoading(true);
    try {
      const response = await ApiService.getCrawlTasks(page, pageSize, status);
      setTasks(response.tasks || []);
      setPagination({
        current: page,
        pageSize,
        total: response.total || 0,
      });
    } catch (error: any) {
      message.error('获取任务列表失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 加载可用的URL列表
  const loadAvailableUrls = async (searchDomain = '') => {
    try {
      setUrlsLoading(true);
      
      // 如果有搜索条件，则只获取匹配的URL；否则获取所有URL
      if (searchDomain.trim()) {
        const response = await ApiService.getUrls(1, 1000, searchDomain.trim());
        setAvailableUrls(response.websites || []);
      } else {
        // 获取所有URL以确保用户能看到完整列表
        const response = await ApiService.getUrls(1, 0, '', true);
        setAvailableUrls(response.websites || []);
        
        // 显示总数提示
        if (response.total > 0) {
          message.success(`成功加载 ${response.total} 个可用URL`);
        }
      }
    } catch (error: any) {
      message.error('获取URL列表失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setUrlsLoading(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadTasks();
    
    // 设置定时刷新（每10秒刷新一次任务状态）
    const interval = setInterval(() => {
      loadTasks(pagination.current, pagination.pageSize);
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  const statusMap: Record<string, { color: string; text: string }> = {
    pending: { color: 'default', text: '待开始' },
    running: { color: 'processing', text: '运行中' },
    paused: { color: 'warning', text: '已暂停' },
    completed: { color: 'success', text: '已完成' },
    stopped: { color: 'error', text: '已停止' },
    failed: { color: 'error', text: '失败' },
  };

  const columns: TableProps<CrawlTask>['columns'] = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (desc: string) => desc || '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const config = statusMap[status] || statusMap.pending;
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '进度',
      key: 'progress',
      width: 250,
      render: (record: CrawlTask) => {
        const completed = record.crawled_urls || 0;
        const total = record.total_urls || 0;
        const failed = record.failed_urls || 0;
        const remaining = total - completed - failed;
        
        let percent = 0;
        if (total > 0) {
          percent = Math.round((completed / total) * 100);
        }

        return (
          <div>
            <Progress 
              percent={percent} 
              size="small" 
              format={() => `${completed}/${total}`}
              status={record.status === 'failed' ? 'exception' : 'normal'}
            />
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
              {record.status === 'running' && (
                <span>
                  <span style={{ color: '#52c41a' }}>成功: {completed}</span>
                  {failed > 0 && <span style={{ color: '#ff4d4f', marginLeft: '8px' }}>失败: {failed}</span>}
                  <span style={{ color: '#1890ff', marginLeft: '8px' }}>剩余: {remaining}</span>
                </span>
              )}
              {record.status === 'completed' && (
                <span style={{ color: '#52c41a' }}>
                  完成率: {total > 0 ? Math.round((completed / total) * 100) : 0}%
                </span>
              )}
              {(record.status === 'stopped' || record.status === 'failed') && (
                <span style={{ color: '#ff4d4f' }}>
                  中断于: {completed}/{total}
                </span>
              )}
            </div>
          </div>
        );
      },
    },
    {
      title: '线程数',
      dataIndex: 'max_threads',
      key: 'max_threads',
      width: 80,
    },
    {
      title: '深度',
      dataIndex: 'max_depth',
      key: 'max_depth',
      width: 80,
    },
    {
      title: '延迟(s)',
      dataIndex: 'request_delay',
      key: 'request_delay',
      width: 90,
    },
    {
      title: 'JS渲染',
      dataIndex: 'enable_js',
      key: 'enable_js',
      width: 90,
      render: (enable: boolean) => (
        <Tag color={enable ? 'green' : 'default'}>
          {enable ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date: string) => new Date(date).toLocaleString('zh-CN'),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="link" 
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewTaskDetail(record.task_id)}
          >
            详情
          </Button>
          {record.status === 'pending' || record.status === 'paused' || record.status === 'stopped' ? (
            <Button
              type="link"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleStartTask(record.task_id)}
            >
              启动
            </Button>
          ) : null}
          {record.status === 'running' && (
            <Button
              type="link"
              size="small"
              icon={<PauseOutlined />}
              onClick={() => handlePauseTask(record.task_id)}
            >
              暂停
            </Button>
          )}
          {(record.status === 'running' || record.status === 'paused' || record.status === 'pending') && (
            <Popconfirm
              title="确定要停止这个任务吗？"
              onConfirm={() => handleStopTask(record.task_id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                size="small"
                danger
                icon={<StopOutlined />}
              >
                停止
              </Button>
            </Popconfirm>
          )}
          {record.status === 'stopped' || record.status === 'completed' || record.status === 'failed' ? (
            <Popconfirm
              title="确定要删除这个任务吗？"
              onConfirm={() => handleDeleteTask(record.task_id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              >
                删除
              </Button>
            </Popconfirm>
          ) : null}
        </Space>
      ),
    },
  ];

  // 创建任务
  const handleCreateTask = async () => {
    try {
      const values = await form.validateFields();
      
      if (selectedUrlIds.length === 0) {
        message.error('请至少选择一个URL进行爬取');
        return;
      }
      
      setLoading(true);
      
      // 构建请求数据，包含selected_urls
      const taskData = {
        name: values.name,
        description: values.description,
        max_threads: values.max_threads,
        max_depth: values.max_depth,
        request_delay: values.request_delay,
        enable_js: values.enable_js,
        same_domain_only: values.same_domain_only,
        lightweight_mode: values.lightweight_mode,
        selected_urls: selectedUrlIds.map(id => parseInt(id)), // 转换为数字数组
      };
      
      await ApiService.createCrawlTask(taskData);
      
      message.success(`任务创建成功，已分配 ${selectedUrlIds.length} 个URL`);
      setCreateModalVisible(false);
      form.resetFields();
      setSelectedUrlIds([]);
      loadTasks(); // 重新加载列表
    } catch (error: any) {
      if (error.response) {
        message.error('创建失败: ' + error.response.data.error);
      } else {
        console.error('表单验证失败:', error);
      }
    } finally {
      setLoading(false);
    }
  };

  // 启动任务
  const handleStartTask = async (taskId: string) => {
    try {
      await ApiService.startCrawlTask(taskId);
      message.success('任务启动成功');
      loadTasks(); // 重新加载列表
    } catch (error: any) {
      message.error('启动失败: ' + (error.response?.data?.error || error.message));
    }
  };

  // 暂停任务
  const handlePauseTask = async (taskId: string) => {
    try {
      await ApiService.pauseCrawlTask(taskId);
      message.success('任务暂停成功');
      loadTasks(); // 重新加载列表
    } catch (error: any) {
      message.error('暂停失败: ' + (error.response?.data?.error || error.message));
    }
  };

  // 停止任务
  const handleStopTask = async (taskId: string) => {
    try {
      await ApiService.stopCrawlTask(taskId);
      message.success('任务停止成功');
      loadTasks(); // 重新加载列表
    } catch (error: any) {
      message.error('停止失败: ' + (error.response?.data?.error || error.message));
    }
  };

  // 删除任务
  const handleDeleteTask = async (taskId: string) => {
    try {
      await ApiService.deleteCrawlTask(taskId);
      message.success('任务删除成功');
      loadTasks(); // 重新加载列表
    } catch (error: any) {
      message.error('删除失败: ' + (error.response?.data?.error || error.message));
    }
  };

  // 查看任务详情
  const handleViewTaskDetail = async (taskId: string) => {
    try {
      setDetailLoading(true);
      const response = await ApiService.getCrawlTaskDetail(taskId);
      setCurrentTaskDetail(response);
      setDetailDrawerVisible(true);
    } catch (error: any) {
      message.error('获取任务详情失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setDetailLoading(false);
    }
  };

  // 表格分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    loadTasks(page, pageSize);
  };

  return (
    <div>
      <Card 
        title="爬虫任务管理"
        extra={
          <Space>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => {
                setCreateModalVisible(true);
                loadAvailableUrls(); // 打开弹窗时加载URL列表
              }}
            >
              创建任务
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={() => loadTasks(pagination.current, pagination.pageSize)}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Table<CrawlTask>
          dataSource={tasks}
          columns={columns}
          loading={loading}
          rowKey="task_id"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            onChange: handleTableChange,
            onShowSizeChange: (current, size) => handleTableChange(current, size),
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      <Modal
        title="创建爬虫任务"
        open={createModalVisible}
        onOk={handleCreateTask}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
          setSelectedUrlIds([]);
        }}
        confirmLoading={loading}
        width={800}
      >
        <Form form={form} layout="vertical" initialValues={{
          max_threads: 4,
          max_depth: 2,
          request_delay: 1,
          enable_js: false,
          same_domain_only: true,
          lightweight_mode: false,
        }}>
          <Form.Item
            name="name"
            label="任务名称"
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <Input placeholder="例如：每日新闻爬取" />
          </Form.Item>

          <Form.Item
            name="description"
            label="任务描述"
          >
            <Input.TextArea 
              placeholder="请输入任务描述（可选）" 
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="max_threads"
            label="最大线程数"
            rules={[{ required: true, message: '请输入线程数' }]}
          >
            <InputNumber 
              min={1} 
              max={20} 
              placeholder="建议1-10" 
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="max_depth"
            label="爬取深度"
            rules={[{ required: true, message: '请输入爬取深度' }]}
          >
            <InputNumber 
              min={1} 
              max={10} 
              placeholder="建议1-5" 
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="request_delay"
            label="请求延迟（秒）"
            rules={[{ required: true, message: '请输入请求延迟' }]}
          >
            <InputNumber 
              min={0.1} 
              max={10} 
              step={0.1}
              placeholder="建议0.5-2秒" 
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="enable_js"
            label="启用JavaScript渲染"
            rules={[{ required: true, message: '请选择是否启用JS渲染' }]}
          >
            <Select placeholder="请选择">
              <Option value={false}>禁用（更快）</Option>
              <Option value={true}>启用（支持动态页面）</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="same_domain_only"
            label="同域名限制"
            tooltip="启用后，爬虫只会爬取与起始URL同域名的链接；禁用后，可以跨域名爬取发现的所有链接"
            rules={[{ required: true, message: '请选择是否限制同域名爬取' }]}
          >
            <Select placeholder="请选择">
              <Option value={true}>启用（仅同域名链接）</Option>
              <Option value={false}>禁用（允许跨域名爬取）</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="lightweight_mode"
            label="爬取模式"
            tooltip="轻量级模式：减少超时时间、简化内容提取，提升爬取速度2-3倍，但仍保持JS渲染支持"
            rules={[{ required: true, message: '请选择爬取模式' }]}
          >
            <Select placeholder="请选择">
              <Option value={false}>标准模式（完整内容提取）</Option>
              <Option value={true}>轻量级模式（快速爬取）</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="选择要爬取的URL"
            required
          >
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                <span style={{ color: '#666', fontSize: '12px' }}>
                  已选择 {selectedUrlIds.length} 个URL，总共 {availableUrls.length} 个可用URL
                </span>
                <Input.Search
                  placeholder="搜索域名..."
                  allowClear
                  style={{ width: 200 }}
                  onSearch={(value) => loadAvailableUrls(value)}
                  loading={urlsLoading}
                />
              </div>
            </div>
            {urlsLoading ? (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <div>正在加载URL列表...</div>
              </div>
            ) : (
              <Transfer
                dataSource={availableUrls.map(url => ({
                  key: url.id.toString(),
                  title: url.url,
                  description: url.domain,
                }))}
                targetKeys={selectedUrlIds}
                onChange={(targetKeys) => setSelectedUrlIds(targetKeys as string[])}
                render={item => (
                  <div>
                    <div style={{ fontWeight: 'bold', fontSize: '13px' }}>{item.title}</div>
                    <div style={{ fontSize: '11px', color: '#666' }}>{item.description}</div>
                  </div>
                )}
                listStyle={{
                  width: 350,
                  height: 400,
                }}
                titles={[`可用URL (${availableUrls.length})`, `已选择URL (${selectedUrlIds.length})`]}
                showSearch
                filterOption={(inputValue, item) => 
                  item.title!.toLowerCase().includes(inputValue.toLowerCase()) ||
                  item.description!.toLowerCase().includes(inputValue.toLowerCase())
                }
              />
            )}
          </Form.Item>
        </Form>
      </Modal>

      <Drawer
        title="任务详情"
        open={detailDrawerVisible}
        onClose={() => setDetailDrawerVisible(false)}
        width={700}
        loading={detailLoading}
      >
        {currentTaskDetail ? (
          <div>
            <div style={{ marginBottom: '24px' }}>
              <h3>基本信息</h3>
              <p><strong>任务名称:</strong> {currentTaskDetail.name}</p>
              <p><strong>任务描述:</strong> {currentTaskDetail.description || '-'}</p>
              <p><strong>状态:</strong> 
                <Tag color={statusMap[currentTaskDetail.status]?.color}>
                  {statusMap[currentTaskDetail.status]?.text || currentTaskDetail.status}
                </Tag>
              </p>
              <p><strong>创建时间:</strong> {new Date(currentTaskDetail.created_at).toLocaleString('zh-CN')}</p>
              {currentTaskDetail.started_at && (
                <p><strong>开始时间:</strong> {new Date(currentTaskDetail.started_at).toLocaleString('zh-CN')}</p>
              )}
              {currentTaskDetail.completed_at && (
                <p><strong>完成时间:</strong> {new Date(currentTaskDetail.completed_at).toLocaleString('zh-CN')}</p>
              )}
            </div>

            <div style={{ marginBottom: '24px' }}>
              <h3>执行进度</h3>
              <Progress 
                percent={currentTaskDetail.total_urls > 0 ? Math.round(((currentTaskDetail.crawled_urls || 0) / currentTaskDetail.total_urls) * 100) : 0}
                format={() => `${currentTaskDetail.crawled_urls || 0}/${currentTaskDetail.total_urls || 0}`}
              />
              <div style={{ marginTop: '8px' }}>
                <span style={{ color: '#52c41a' }}>✅ 成功: {currentTaskDetail.crawled_urls || 0}</span>
                <span style={{ color: '#ff4d4f', marginLeft: '16px' }}>❌ 失败: {currentTaskDetail.failed_urls || 0}</span>
                <span style={{ color: '#1890ff', marginLeft: '16px' }}>⏳ 剩余: {
                  (currentTaskDetail.total_urls || 0) - (currentTaskDetail.crawled_urls || 0) - (currentTaskDetail.failed_urls || 0)
                }</span>
                <span style={{ color: '#722ed1', marginLeft: '16px' }}>📊 成功率: {
                  currentTaskDetail.total_urls > 0 ? 
                    Math.round(((currentTaskDetail.crawled_urls || 0) / currentTaskDetail.total_urls) * 100) : 0
                }%</span>
              </div>
            </div>

            <div style={{ marginBottom: '24px' }}>
              <h3>配置参数</h3>
              <p><strong>最大线程数:</strong> {currentTaskDetail.max_threads}</p>
              <p><strong>爬取深度:</strong> {currentTaskDetail.max_depth}</p>
              <p><strong>请求延迟:</strong> {currentTaskDetail.request_delay}秒</p>
              <p><strong>启用JS渲染:</strong> {currentTaskDetail.enable_js ? '是' : '否'}</p>
            </div>

            {currentTaskDetail.recent_crawled_urls && currentTaskDetail.recent_crawled_urls.length > 0 && (
              <div style={{ marginBottom: '24px' }}>
                <h3>最近抓取的URL</h3>
                <List
                  size="small"
                  dataSource={currentTaskDetail.recent_crawled_urls}
                  renderItem={(item: any) => (
                    <List.Item>
                      <div style={{ width: '100%' }}>
                        <div>
                          <Text ellipsis style={{ maxWidth: '400px' }}>
                            <a href={item.url} target="_blank" rel="noopener noreferrer">
                              {item.url}
                            </a>
                          </Text>
                          <Tag color={item.status === 'completed' ? 'green' : 'red'} style={{ marginLeft: '8px' }}>
                            {item.response_code}
                          </Tag>
                        </div>
                        {item.title && (
                          <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                            标题: {item.title}
                          </div>
                        )}
                        <div style={{ fontSize: '12px', color: '#999' }}>
                          {new Date(item.crawled_at).toLocaleString('zh-CN')}
                        </div>
                      </div>
                    </List.Item>
                  )}
                />
              </div>
            )}

            {currentTaskDetail.assigned_urls && currentTaskDetail.assigned_urls.length > 0 && (
              <div>
                <h3>分配的URL (前10个)</h3>
                <List
                  size="small"
                  dataSource={currentTaskDetail.assigned_urls}
                  renderItem={(item: any) => (
                    <List.Item>
                      <Text ellipsis style={{ maxWidth: '500px' }}>
                        {item.url}
                      </Text>
                      <Tag>{item.domain}</Tag>
                    </List.Item>
                  )}
                />
                {currentTaskDetail.assigned_urls_count > 10 && (
                  <div style={{ textAlign: 'center', marginTop: '8px', color: '#666' }}>
                    还有 {currentTaskDetail.assigned_urls_count - 10} 个URL...
                  </div>
                )}
              </div>
            )}
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Text type="secondary">暂无任务详情</Text>
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default CrawlerTasks; 