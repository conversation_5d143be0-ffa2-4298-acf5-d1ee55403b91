#!/bin/bash
# 启动所有服务的脚本

echo "🚀 启动内容监测系统所有服务..."

# 检查Redis是否安装
if ! command -v redis-server &> /dev/null; then
    echo "❌ Redis未安装，请先安装Redis："
    echo "   brew install redis"
    exit 1
fi

# 启动Redis（如果未运行）
if ! pgrep -x "redis-server" > /dev/null; then
    echo "🔴 启动Redis服务..."
    redis-server --daemonize yes
    sleep 2
    echo "✅ Redis已启动"
else
    echo "✅ Redis已在运行"
fi

# 检查Redis连接
redis-cli ping > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Redis连接正常"
else
    echo "❌ Redis连接失败"
    exit 1
fi

echo ""
echo "📋 接下来请分别在不同终端窗口中运行："
echo "1. 启动后端服务:"
echo "   cd backend && source venv/bin/activate && python app.py"
echo ""
echo "2. 启动Celery Worker:"
echo "   ./start_celery.sh"
echo ""
echo "3. 启动前端服务:"
echo "   cd frontend && npm start"
echo ""
echo "🎉 所有准备工作完成！"