#!/usr/bin/env python3
"""
测试same_domain_only参数的API端点
"""
import requests
import json

# API端点
BASE_URL = "http://localhost:5001"
LOGIN_URL = f"{BASE_URL}/api/auth/login"
CREATE_TASK_URL = f"{BASE_URL}/api/crawler/tasks"

def test_same_domain_parameter():
    """测试same_domain_only参数"""
    
    # 1. 首先登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    print("🔐 正在登录...")
    login_response = requests.post(LOGIN_URL, json=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.text}")
        return
    
    token = login_response.json().get('access_token')
    headers = {"Authorization": f"Bearer {token}"}
    
    print("✅ 登录成功")
    
    # 2. 创建测试任务（开启同域名限制）
    test_task_with_domain_restriction = {
        "name": "测试任务-同域名限制",
        "description": "测试same_domain_only=true参数",
        "selected_urls": [1],  # 假设存在ID为1的URL
        "max_threads": 2,
        "max_depth": 2,
        "request_delay": 1.0,
        "enable_js": True,
        "same_domain_only": True  # 开启同域名限制
    }
    
    print("🚀 创建任务（同域名限制开启）...")
    response1 = requests.post(CREATE_TASK_URL, json=test_task_with_domain_restriction, headers=headers)
    
    if response1.status_code == 201:
        task_data = response1.json()
        print("✅ 任务创建成功:")
        print(f"   任务ID: {task_data['task']['task_id']}")
        print(f"   同域名限制: {task_data['task']['same_domain_only']}")
    else:
        print(f"❌ 任务创建失败: {response1.text}")
    
    # 3. 创建测试任务（关闭同域名限制）
    test_task_without_domain_restriction = {
        "name": "测试任务-跨域允许",
        "description": "测试same_domain_only=false参数",
        "selected_urls": [1],  # 假设存在ID为1的URL
        "max_threads": 2,
        "max_depth": 2,
        "request_delay": 1.0,
        "enable_js": True,
        "same_domain_only": False  # 关闭同域名限制
    }
    
    print("🚀 创建任务（同域名限制关闭）...")
    response2 = requests.post(CREATE_TASK_URL, json=test_task_without_domain_restriction, headers=headers)
    
    if response2.status_code == 201:
        task_data = response2.json()
        print("✅ 任务创建成功:")
        print(f"   任务ID: {task_data['task']['task_id']}")
        print(f"   同域名限制: {task_data['task']['same_domain_only']}")
    else:
        print(f"❌ 任务创建失败: {response2.text}")
    
    # 4. 测试默认值（如果不提供same_domain_only参数）
    test_task_default = {
        "name": "测试任务-默认值",
        "description": "测试same_domain_only默认值",
        "selected_urls": [1],  # 假设存在ID为1的URL
        "max_threads": 2,
        "max_depth": 2,
        "request_delay": 1.0,
        "enable_js": True
        # 不提供same_domain_only参数，应该使用默认值True
    }
    
    print("🚀 创建任务（测试默认值）...")
    response3 = requests.post(CREATE_TASK_URL, json=test_task_default, headers=headers)
    
    if response3.status_code == 201:
        task_data = response3.json()
        print("✅ 任务创建成功:")
        print(f"   任务ID: {task_data['task']['task_id']}")
        print(f"   同域名限制: {task_data['task']['same_domain_only']} (默认值)")
    else:
        print(f"❌ 任务创建失败: {response3.text}")

if __name__ == "__main__":
    test_same_domain_parameter()