#!/usr/bin/env python3
"""
Celery异步任务 - 使用crawl4ai进行网页爬取
"""
import asyncio
from datetime import datetime
from app.celery_app import celery
from app.extensions import db
from app.models import CrawlTask, Website, CrawlResult
from app.models.task_urls import TaskUrl

@celery.task(bind=True, name='crawler_tasks.crawl_with_crawl4ai')
def crawl_with_crawl4ai(self, task_id):
    """
    使用crawl4ai异步爬取任务
    """
    try:
        from crawl4ai import AsyncWebCrawler
        from app import create_app
        
        # 创建Flask应用上下文
        app = create_app()
        with app.app_context():
            # 获取任务信息
            task = CrawlTask.query.filter_by(task_id=task_id).first()
            if not task:
                return {'error': '任务不存在'}
            
            # 获取任务关联的网站
            task_websites = task.websites
            if not task_websites:
                return {'error': '任务没有分配的URL'}
            
            # 更新任务状态
            task.status = 'running'
            task.started_at = datetime.utcnow()
            db.session.commit()
        
        crawled_count = 0
        failed_count = 0
        results = []
        
        print(f"🚀 [crawl4ai-celery] 开始爬取任务: {task.name} ({len(task_websites)} 个URL)")
        
        # 创建新的事件循环运行crawl4ai
        async def run_crawl4ai():
            nonlocal crawled_count, failed_count, results
            
            async with AsyncWebCrawler(
                verbose=True,
                headless=True,
                browser_type="chromium"
            ) as crawler:
                
                for website in task_websites:
                    try:
                        print(f"🔄 [crawl4ai] 开始爬取: {website.url}")
                        
                        # 智能处理URL格式
                        url = website.url.strip()
                        if not url.startswith(('http://', 'https://')):
                            url = 'http://' + url
                        
                        # 使用crawl4ai爬取网页（支持JS渲染）
                        result = await crawler.arun(
                            url=url,
                            word_count_threshold=10,
                            bypass_cache=True,
                            css_selector=None,
                            screenshot=False,
                            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                            verbose=True,
                            # JavaScript 渲染支持
                            js_code=["window.scrollTo(0, document.body.scrollHeight);"],
                            wait_for=2.0,
                            # 反爬虫设置
                            delay_before_return_html=1.0,
                            simulate_user=True,
                            override_navigator=True,
                            magic=True
                        )
                        
                        if result.success:
                            # 提取内容
                            final_url = result.url or url
                            title = ''
                            if result.metadata:
                                title = result.metadata.get('title', '')
                            
                            # 优先使用清理后的文本
                            if result.cleaned_html:
                                text_content = result.cleaned_html[:2000]
                            elif result.markdown:
                                text_content = result.markdown[:2000]
                            else:
                                text_content = result.html[:2000] if result.html else ''
                            
                            # 创建成功的爬取结果
                            crawl_result = CrawlResult(
                                url=final_url,
                                website_id=website.id,
                                crawl_task_id=task.id,
                                depth=0
                            )
                            crawl_result.title = title[:500] if title else ''
                            crawl_result.content_text = text_content
                            crawl_result.content_html = result.html[:10000] if result.html else ''
                            crawl_result.response_code = getattr(result, 'status_code', 200)
                            crawl_result.content_type = 'text/html'
                            crawl_result.content_length = len(result.html) if result.html else 0
                            crawl_result.status = 'completed'
                            crawl_result.crawled_at = datetime.utcnow()
                            
                            # 检查重定向
                            if final_url != url:
                                crawl_result.redirect_url = final_url
                                crawl_result.redirect_count = 1
                            
                            db.session.add(crawl_result)
                            
                            # 调用检测服务
                            try:
                                from app.services.detection_service import DetectionService
                                detection_service = DetectionService()
                                detection_service.detect_content(crawl_result.id, text_content)
                            except Exception as detection_e:
                                print(f"⚠️ 内容检测失败: {detection_e}")
                            
                            # 更新进度
                            crawled_count += 1
                            task.crawled_urls = crawled_count
                            task.progress = (crawled_count / len(task_websites)) * 100
                            db.session.commit()
                            
                            print(f"✅ [crawl4ai] 成功爬取: {website.url} (进度: {task.progress:.1f}%)")
                            print(f"   📄 标题: {title}")
                            print(f"   📊 内容长度: {crawl_result.content_length}")
                            
                            results.append({
                                'url': website.url,
                                'title': title,
                                'status': 'success',
                                'response_code': crawl_result.response_code,
                                'content_length': crawl_result.content_length,
                                'js_rendered': True
                            })
                            
                        else:
                            # 爬取失败
                            error_message = getattr(result, 'error_message', None) or "crawl4ai爬取失败"
                            print(f"❌ [crawl4ai] 爬取失败 {website.url}: {error_message}")
                            
                            # 创建失败记录
                            failed_result = CrawlResult(
                                url=url,
                                website_id=website.id,
                                crawl_task_id=task.id,
                                depth=0
                            )
                            failed_result.status = 'failed'
                            failed_result.error_message = f"crawl4ai: {error_message}"
                            failed_result.crawled_at = datetime.utcnow()
                            failed_result.response_code = 0
                            
                            db.session.add(failed_result)
                            
                            failed_count += 1
                            task.failed_urls = failed_count
                            db.session.commit()
                            
                            results.append({
                                'url': website.url,
                                'status': 'failed',
                                'error_type': 'CrawlError',
                                'error': error_message,
                                'response_code': 0,
                                'js_rendered': False
                            })
                        
                        # 延迟
                        if task.request_delay > 0:
                            await asyncio.sleep(task.request_delay)
                            
                    except Exception as e:
                        error_type = type(e).__name__
                        error_message = str(e)
                        print(f"❌ [crawl4ai] 爬取异常 {website.url}: {error_type}: {error_message}")
                        
                        # 创建失败记录
                        failed_result = CrawlResult(
                            url=website.url,
                            website_id=website.id,
                            crawl_task_id=task.id,
                            depth=0
                        )
                        failed_result.status = 'failed'
                        failed_result.error_message = f"{error_type}: {error_message}"
                        failed_result.crawled_at = datetime.utcnow()
                        failed_result.response_code = 500
                        
                        db.session.add(failed_result)
                        
                        failed_count += 1
                        task.failed_urls = failed_count
                        db.session.commit()
                        
                        results.append({
                            'url': website.url,
                            'status': 'failed',
                            'error_type': error_type,
                            'error': error_message,
                            'response_code': 500,
                            'js_rendered': False
                        })
        
        # 运行异步爬虫（在Celery worker中没有事件循环冲突）
        asyncio.run(run_crawl4ai())
        
        # 标记任务完成
        task.status = 'completed'
        task.completed_at = datetime.utcnow()
        task.total_urls = len(task_websites)
        db.session.commit()
        
        print(f"🎉 [crawl4ai-celery] 任务完成! 成功: {crawled_count}, 失败: {failed_count}")
        
        return {
            'task_id': task_id,
            'status': 'completed',
            'total_urls': len(task_websites),
            'crawled_urls': crawled_count,
            'failed_urls': failed_count,
            'engine': 'crawl4ai',
            'js_support': True,
            'results': results
        }
        
    except Exception as e:
        # 任务执行失败
        print(f"💥 [crawl4ai-celery] 任务执行失败: {e}")
        try:
            task = CrawlTask.query.filter_by(task_id=task_id).first()
            if task:
                task.status = 'failed'
                task.completed_at = datetime.utcnow()
                db.session.commit()
        except:
            pass
        
        return {
            'task_id': task_id,
            'status': 'failed',
            'error': str(e),
            'engine': 'crawl4ai'
        } 