#!/usr/bin/env python3
"""
Celery异步任务 - 使用crawl4ai进行网页爬取（支持深度抓取）
"""
import asyncio
import aiohttp
import re
import mimetypes
from datetime import datetime
from urllib.parse import urljoin, urlparse
from typing import Set, List, Dict, Tuple
from bs4 import BeautifulSoup
from app.celery_app import celery
from app.extensions import db
from app.models import CrawlTask, Website, CrawlResult
from app.models.crawl_result import PageImage
from app.models.task_urls import TaskUrl

def extract_links_from_html(html_content: str, base_url: str) -> List[str]:
    """
    从HTML内容中提取所有链接
    """
    links = []
    if not html_content:
        return links
    
    # 使用正则表达式提取href属性
    href_pattern = r'<a[^>]+href=["\']([^"\']+)["\'][^>]*>'
    matches = re.findall(href_pattern, html_content, re.IGNORECASE)
    
    for href in matches:
        # 跳过非HTTP链接
        if href.startswith(('mailto:', 'tel:', 'javascript:', '#')):
            continue
            
        # 处理相对链接
        if href.startswith(('http://', 'https://')):
            absolute_url = href
        else:
            absolute_url = urljoin(base_url, href)
        
        # 清理URL（移除fragment）
        parsed = urlparse(absolute_url)
        clean_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        if parsed.query:
            clean_url += f"?{parsed.query}"
            
        if clean_url not in links:
            links.append(clean_url)
    
    return links

async def extract_and_save_images(html_content: str, base_url: str, crawl_result_id: int, lightweight_mode: bool = False) -> int:
    """
    从HTML内容中提取图片并保存到数据库
    """
    if not html_content:
        return 0
    
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        img_tags = soup.find_all('img')
        
        if not img_tags:
            return 0
        
        images_saved = 0
        max_images = 10 if lightweight_mode else 50  # 轻量级模式限制图片数量
        
        for img_tag in img_tags[:max_images]:
            try:
                # 获取图片URL
                src = img_tag.get('src')
                if not src:
                    continue
                
                # 转换为绝对URL
                img_url = urljoin(base_url, src)
                
                # 验证URL格式
                parsed_url = urlparse(img_url)
                if not parsed_url.scheme or not parsed_url.netloc:
                    continue
                
                # 获取图片的alt文本
                alt_text = img_tag.get('alt', '')
                
                # 获取图片尺寸信息（如果有）
                width = None
                height = None
                try:
                    if img_tag.get('width'):
                        width = int(img_tag.get('width'))
                    if img_tag.get('height'):
                        height = int(img_tag.get('height'))
                except (ValueError, TypeError):
                    pass
                
                # 推测图片格式
                img_format = None
                if src.lower().endswith(('.jpg', '.jpeg')):
                    img_format = 'JPEG'
                elif src.lower().endswith('.png'):
                    img_format = 'PNG'
                elif src.lower().endswith('.gif'):
                    img_format = 'GIF'
                elif src.lower().endswith('.webp'):
                    img_format = 'WEBP'
                elif src.lower().endswith('.svg'):
                    img_format = 'SVG'
                
                # 过滤掉明显的小图标（可选）
                if width and height and (width < 20 or height < 20) and lightweight_mode:
                    continue
                
                # 检查URL是否已存在
                existing_image = PageImage.query.filter_by(
                    url=img_url, 
                    crawl_result_id=crawl_result_id
                ).first()
                
                if existing_image:
                    continue
                
                # 创建PageImage记录
                page_image = PageImage(
                    url=img_url,
                    crawl_result_id=crawl_result_id,
                    alt_text=alt_text[:500] if alt_text else None,
                    width=width,
                    height=height,
                    format=img_format
                )
                
                db.session.add(page_image)
                images_saved += 1
                
            except Exception as e:
                print(f"⚠️ 处理图片时出错: {e}")
                continue
        
        if images_saved > 0:
            print(f"🖼️ 提取并保存了 {images_saved} 个图片")
        
        return images_saved
        
    except Exception as e:
        print(f"❌ 图片提取失败: {e}")
        return 0

def is_same_domain(url1: str, url2: str) -> bool:
    """
    检查两个URL是否属于同一域名
    """
    try:
        domain1 = urlparse(url1).netloc.lower()
        domain2 = urlparse(url2).netloc.lower()
        
        # 移除www前缀进行比较
        domain1 = domain1.replace('www.', '')
        domain2 = domain2.replace('www.', '')
        
        return domain1 == domain2
    except:
        return False

def should_crawl_url(url: str, seed_urls: List[str], max_depth: int, current_depth: int, same_domain_only: bool = True) -> bool:
    """
    判断是否应该爬取该URL
    """
    if current_depth >= max_depth:
        return False
    
    # 如果不限制同域名，则所有HTTP/HTTPS链接都可以爬取
    if not same_domain_only:
        return url.startswith(('http://', 'https://'))
    
    # 只爬取与种子URL同域名的页面
    for seed_url in seed_urls:
        if is_same_domain(url, seed_url):
            return True
    
    return False

@celery.task(bind=True, name='crawler_tasks.crawl_with_crawl4ai')
def crawl_with_crawl4ai(self, task_id):
    """
    使用crawl4ai异步爬取任务（带Flask应用上下文）
    """
    try:
        from crawl4ai import AsyncWebCrawler
        from app import create_app
        
        # 创建Flask应用上下文
        app = create_app()
        
        with app.app_context():
            # 获取任务信息
            task = CrawlTask.query.filter_by(task_id=task_id).first()
            if not task:
                return {'error': '任务不存在', 'task_id': task_id}
            
            # 获取任务关联的网站
            task_websites = task.websites
            if not task_websites:
                return {'error': '任务没有分配的URL', 'task_id': task_id}
            
            # 更新任务状态
            task.status = 'running'
            task.started_at = datetime.utcnow()
            db.session.commit()
            
            crawled_count = 0
            failed_count = 0
            results = []
            
            print(f"🚀 [crawl4ai-celery] 开始爬取任务: {task.name} ({len(task_websites)} 个URL)")
            
            # 初始化深度爬取数据结构
            seed_urls = [website.url for website in task_websites]
            visited_urls: Set[str] = set()
            url_queue: List[Tuple[str, int, int]] = []  # (url, depth, website_id)
            
            # 将种子URL加入队列（深度为0）
            for website in task_websites:
                clean_url = website.url.strip()
                if not clean_url.startswith(('http://', 'https://')):
                    clean_url = 'http://' + clean_url
                url_queue.append((clean_url, 0, website.id))
            
            # 设置默认最大页面数（如果没有设置的话）
            max_pages = getattr(task, 'max_pages', 100)  # 默认最多100页
            
            print(f"🚀 [crawl4ai-celery] 开始深度爬取任务: {task.name}")
            print(f"   📊 种子URL: {len(seed_urls)} 个")
            print(f"   🏗️ 最大深度: {task.max_depth}")
            print(f"   📄 最大页面数: {max_pages}")
            print(f"   🌐 同域名限制: {'开启' if task.same_domain_only else '关闭'}")
            
            # 创建新的事件循环运行并发crawl4ai
            async def run_concurrent_depth_crawl():
                nonlocal crawled_count, failed_count, results, visited_urls, url_queue
                
                # 并发处理单个URL的函数（带重试机制）
                async def process_single_url(crawler, url_data, max_retries=2):
                    current_url, current_depth, source_website_id = url_data
                    
                    # 避免重复处理
                    if current_url in visited_urls:
                        return None
                    
                    visited_urls.add(current_url)
                    
                    # 找到对应的源网站记录
                    source_website = None
                    if current_depth == 0:
                        source_website = Website.query.get(source_website_id)
                    else:
                        for website in task_websites:
                            if is_same_domain(current_url, website.url):
                                source_website = website
                                break
                        if not source_website and not task.same_domain_only:
                            source_website = task_websites[0]
                    
                    if not source_website:
                        print(f"⚠️ 无法找到URL对应的源网站: {current_url}")
                        return None
                    
                    # 重试机制
                    for retry_count in range(max_retries + 1):
                        try:
                            if retry_count > 0:
                                print(f"🔄 [crawl4ai] 重试 {retry_count}/{max_retries} (深度{current_depth}): {current_url}")
                                # 重试间隔递增
                                await asyncio.sleep(retry_count * 1.0)
                            else:
                                print(f"🔄 [crawl4ai] 爬取 (深度{current_depth}): {current_url}")
                            
                            # 根据重试次数和轻量级模式调整超时时间
                            if task.lightweight_mode:
                                base_timeout = 6000 if retry_count == 0 else 10000  # 轻量级：首次6秒，重试10秒
                                wait_time = 0.5 if task.enable_js else 0.2  # 减少等待时间
                                word_threshold = 3  # 降低词数阈值，快速跳过空页面
                            else:
                                base_timeout = 8000 if retry_count == 0 else 12000  # 普通：首次8秒，重试12秒
                                wait_time = 0.8 if task.enable_js else 0.3
                                word_threshold = 5
                            
                            # 高性能crawl4ai配置（保持JS渲染支持）
                            crawl_config = {
                                "word_count_threshold": word_threshold,
                                "bypass_cache": True,
                                "css_selector": None,
                                "screenshot": False,
                                "page_timeout": base_timeout,
                                "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                                # 始终支持JS渲染（动态网页必需）
                                "js_code": ["window.scrollTo(0, document.body.scrollHeight);"] if task.enable_js else [],
                                "wait_for": wait_time,
                                "delay_before_return_html": 0.1 if task.lightweight_mode else 0.2,
                                "simulate_user": True,
                                "override_navigator": True,
                                "magic": False,
                            }
                            
                            # 使用asyncio.wait_for为整个爬取操作添加超时
                            try:
                                result = await asyncio.wait_for(
                                    crawler.arun(url=current_url, **crawl_config),
                                    timeout=base_timeout / 1000 + 3  # 比crawl4ai超时多3秒
                                )
                                
                                if result.success:
                                    if retry_count > 0:
                                        print(f"✅ [crawl4ai] 重试成功 (深度{current_depth}): {current_url}")
                                    return await handle_successful_crawl(result, current_url, current_depth, source_website)
                                else:
                                    error_msg = getattr(result, 'error_message', "crawl4ai爬取失败")
                                    
                                    # 某些错误不值得重试
                                    if not should_retry_error(error_msg) or retry_count >= max_retries:
                                        return await handle_failed_crawl(current_url, current_depth, source_website, error_msg)
                                    
                                    print(f"⚠️ [crawl4ai] 爬取失败，准备重试: {error_msg}")
                                    continue  # 继续重试
                                    
                            except asyncio.TimeoutError:
                                timeout_msg = f"爬取超时 ({base_timeout/1000:.1f}s)"
                                if retry_count >= max_retries:
                                    return await handle_failed_crawl(current_url, current_depth, source_website, timeout_msg)
                                print(f"⚠️ [crawl4ai] {timeout_msg}，准备重试")
                                continue  # 继续重试
                                
                        except Exception as e:
                            error_type = type(e).__name__
                            error_message = str(e)
                            
                            # 某些异常不值得重试
                            if not should_retry_error(error_message) or retry_count >= max_retries:
                                print(f"❌ [crawl4ai] 爬取异常 (深度{current_depth}): {current_url}: {error_type}: {error_message}")
                                return await handle_failed_crawl(current_url, current_depth, source_website, f"{error_type}: {error_message}", 500)
                            
                            print(f"⚠️ [crawl4ai] 爬取异常，准备重试: {error_type}: {error_message}")
                            continue  # 继续重试
                    
                    # 所有重试都失败了
                    return await handle_failed_crawl(current_url, current_depth, source_website, "重试次数已用尽", 500)
                
                # 判断错误是否值得重试
                def should_retry_error(error_msg):
                    """判断错误是否值得重试"""
                    if not error_msg:
                        return True
                    
                    error_msg_lower = error_msg.lower()
                    
                    # 不值得重试的错误（明确的失败）
                    no_retry_errors = [
                        'not found',           # 404错误
                        'forbidden',           # 403错误  
                        'unauthorized',        # 401错误
                        'bad request',         # 400错误
                        'invalid url',         # URL格式错误
                        'dns_probe_finished_nxdomain',  # DNS解析失败
                        'ssl_protocol_error',  # SSL协议错误
                        'certificate',         # 证书错误
                    ]
                    
                    for no_retry in no_retry_errors:
                        if no_retry in error_msg_lower:
                            return False
                    
                    # 值得重试的错误（临时性问题）
                    retry_errors = [
                        'timeout',             # 超时错误
                        'connection_reset',    # 连接重置
                        'connection_refused',  # 连接拒绝
                        'empty_response',      # 空响应
                        'net::err',           # 网络错误
                        'timed_out',          # 超时
                        'connection',         # 连接问题
                        'temporarily',        # 临时问题
                    ]
                    
                    for retry_error in retry_errors:
                        if retry_error in error_msg_lower:
                            return True
                    
                    # 默认重试未知错误
                    return True
                
                # 处理成功爬取的函数
                async def handle_successful_crawl(result, current_url, current_depth, source_website):
                    nonlocal crawled_count
                    
                    final_url = result.url or current_url
                    title = result.metadata.get('title', '') if result.metadata else ''
                    
                    # 根据轻量级模式调整内容提取
                    if task.lightweight_mode:
                        # 轻量级模式：只提取少量关键内容
                        text_content = (result.cleaned_html or result.markdown or result.html or '')[:500]
                        html_content = (result.html or '')[:2000]  # 减少HTML存储
                    else:
                        # 普通模式：提取完整内容
                        if result.cleaned_html:
                            text_content = result.cleaned_html[:2000]
                        elif result.markdown:
                            text_content = result.markdown[:2000]
                        else:
                            text_content = result.html[:2000] if result.html else ''
                        html_content = result.html[:10000] if result.html else ''
                    
                    # 创建成功记录
                    crawl_result = CrawlResult(
                        url=final_url,
                        website_id=source_website.id,
                        crawl_task_id=task.id,
                        depth=current_depth
                    )
                    crawl_result.title = title[:500] if title else ''
                    crawl_result.content_text = text_content
                    crawl_result.content_html = html_content
                    crawl_result.response_code = getattr(result, 'status_code', 200)
                    crawl_result.content_type = 'text/html'
                    crawl_result.content_length = len(result.html) if result.html else 0
                    crawl_result.status = 'completed'
                    crawl_result.crawled_at = datetime.utcnow()
                    
                    if final_url != current_url:
                        crawl_result.redirect_url = final_url
                        crawl_result.redirect_count = 1
                    
                    # 提取链接用于深度爬取
                    new_links = []
                    if current_depth < task.max_depth:
                        extracted_links = extract_links_from_html(result.html or '', final_url)
                        for link in extracted_links:
                            if (link not in visited_urls and 
                                should_crawl_url(link, seed_urls, task.max_depth, current_depth + 1, task.same_domain_only)):
                                new_links.append((link, current_depth + 1, source_website.id))
                        
                        if new_links:
                            print(f"   🔗 发现 {len(extracted_links)} 个链接，添加 {len(new_links)} 个到队列 (深度{current_depth + 1})")
                    
                    db.session.add(crawl_result)
                    db.session.flush()  # 获取crawl_result.id用于图片关联
                    
                    # 提取和处理图片
                    images_count = 0
                    try:
                        images_count = await extract_and_save_images(result.html or '', final_url, crawl_result.id, task.lightweight_mode)
                        crawl_result.images_count = images_count
                    except Exception as img_e:
                        print(f"⚠️ 图片提取失败: {img_e}")
                    
                    # 内容检测（轻量级模式可选择性跳过复杂检测）
                    if not task.lightweight_mode or len(text_content) > 100:  # 轻量级模式跳过内容过少的页面
                        try:
                            from app.services.detection_service import DetectionService
                            detection_service = DetectionService()
                            detection_service.detect_content(crawl_result.id)
                        except Exception as detection_e:
                            print(f"⚠️ 内容检测失败: {detection_e}")
                    else:
                        print(f"🚀 [轻量级模式] 跳过内容检测: 内容过少 ({len(text_content)} 字符)")
                    
                    crawled_count += 1
                    task.crawled_urls = crawled_count
                    total_processed = crawled_count + failed_count
                    task.progress = min((total_processed / max_pages) * 100, 100.0)
                    db.session.commit()
                    
                    print(f"✅ [crawl4ai] 成功爬取 (深度{current_depth}): {current_url}")
                    print(f"   📄 标题: {title}")
                    print(f"   📊 内容长度: {crawl_result.content_length}")
                    print(f"   📈 进度: {crawled_count}/{max_pages} ({task.progress:.1f}%)")
                    
                    return {
                        'url': current_url,
                        'title': title,
                        'status': 'success',
                        'depth': current_depth,
                        'response_code': crawl_result.response_code,
                        'content_length': crawl_result.content_length,
                        'js_rendered': True,
                        'new_links': new_links
                    }
                
                # 处理失败爬取的函数
                async def handle_failed_crawl(current_url, current_depth, source_website, error_message, response_code=0):
                    nonlocal failed_count
                    
                    print(f"❌ [crawl4ai] 爬取失败 (深度{current_depth}): {current_url}: {error_message}")
                    
                    failed_result = CrawlResult(
                        url=current_url,
                        website_id=source_website.id if source_website else task_websites[0].id,
                        crawl_task_id=task.id,
                        depth=current_depth
                    )
                    failed_result.status = 'failed'
                    failed_result.error_message = error_message
                    failed_result.crawled_at = datetime.utcnow()
                    failed_result.response_code = response_code
                    
                    db.session.add(failed_result)
                    
                    failed_count += 1
                    task.failed_urls = failed_count
                    total_processed = crawled_count + failed_count
                    task.progress = min((total_processed / max_pages) * 100, 100.0)
                    db.session.commit()
                    
                    return {
                        'url': current_url,
                        'status': 'failed',
                        'depth': current_depth,
                        'error_type': 'CrawlError',
                        'error': error_message,
                        'response_code': response_code,
                        'js_rendered': False
                    }
                
                # 主并发处理循环
                async with AsyncWebCrawler(
                    headless=True,
                    browser_type="chromium"
                ) as crawler:
                    
                    while url_queue and crawled_count + failed_count < max_pages:
                        # 计算当前批次大小（基于线程数）
                        batch_size = min(task.max_threads, len(url_queue), max_pages - (crawled_count + failed_count))
                        if batch_size <= 0:
                            break
                        
                        # 取出一批URL进行并发处理
                        current_batch = []
                        for _ in range(batch_size):
                            if url_queue:
                                current_batch.append(url_queue.pop(0))
                        
                        if not current_batch:
                            break
                        
                        print(f"🚀 [crawl4ai] 开始并发处理 {len(current_batch)} 个URL...")
                        
                        # 创建并发任务
                        tasks = [process_single_url(crawler, url_data) for url_data in current_batch]
                        
                        # 并发执行，带有超时保护
                        try:
                            batch_results = await asyncio.wait_for(
                                asyncio.gather(*tasks, return_exceptions=True),
                                timeout=30.0  # 整个批次30秒超时
                            )
                            
                            # 处理批次结果
                            for i, batch_result in enumerate(batch_results):
                                if isinstance(batch_result, Exception):
                                    print(f"⚠️ 批次任务异常: {batch_result}")
                                elif batch_result:
                                    results.append(batch_result)
                                    # 将新发现的链接加入队列
                                    if 'new_links' in batch_result and batch_result['new_links']:
                                        url_queue.extend(batch_result['new_links'])
                            
                        except asyncio.TimeoutError:
                            print(f"⚠️ 批次处理超时，跳过当前批次")
                            continue
                        
                        # 批次间延迟
                        if task.request_delay > 0:
                            await asyncio.sleep(task.request_delay * 0.5)  # 批次间减半延迟
            
            # 运行并发深度爬取（在Celery worker中没有事件循环冲突）
            asyncio.run(run_concurrent_depth_crawl())
            
            # 标记任务完成
            task.status = 'completed'
            task.completed_at = datetime.utcnow()
            task.total_urls = crawled_count + failed_count  # 实际处理的URL总数
            db.session.commit()
            
            print(f"🎉 [crawl4ai-celery] 深度爬取任务完成!")
            print(f"   📊 统计: 成功 {crawled_count}, 失败 {failed_count}")
            print(f"   🔗 总处理: {len(visited_urls)} 个URL")
            print(f"   📈 队列剩余: {len(url_queue)} 个URL")
            
            return {
                'task_id': task_id,
                'status': 'completed',
                'total_urls': crawled_count + failed_count,
                'crawled_urls': crawled_count,
                'failed_urls': failed_count,
                'max_depth': task.max_depth,
                'visited_urls': len(visited_urls),
                'engine': 'crawl4ai-depth',
                'depth_enabled': True,
                'js_support': True,
                'results': results
            }
        
    except Exception as e:
        # 任务执行失败
        print(f"💥 [crawl4ai-celery] 任务执行失败: {e}")
        
        # 在应用上下文中标记任务失败
        try:
            from app import create_app
            app = create_app()
            with app.app_context():
                task = CrawlTask.query.filter_by(task_id=task_id).first()
                if task:
                    task.status = 'failed'
                    task.completed_at = datetime.utcnow()
                    db.session.commit()
        except Exception as ctx_e:
            print(f"💥 应用上下文错误: {ctx_e}")
        
        return {
            'task_id': task_id,
            'status': 'failed',
            'error': str(e),
            'engine': 'crawl4ai'
        }