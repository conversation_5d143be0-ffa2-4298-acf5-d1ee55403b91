"""
导出服务
处理检测结果导出相关业务逻辑
"""

import os
import pandas as pd
from datetime import datetime
from typing import List, Dict, Optional
from flask import current_app
import logging

from app.models import DetectionResult, CrawlResult, Website

logger = logging.getLogger(__name__)

class ExportService:
    """导出服务类"""
    
    def __init__(self):
        self.export_folder = current_app.config.get('EXPORT_FOLDER', 'exports')
        os.makedirs(self.export_folder, exist_ok=True)
    
    def export_detection_results_to_excel(self, filters: Dict = None) -> Dict:
        """导出检测结果到Excel"""
        try:
            # 获取检测结果数据
            results_data = self._get_detection_results_data(filters)
            
            if not results_data:
                return {
                    'success': False,
                    'message': '没有找到符合条件的检测结果'
                }
            
            # 创建DataFrame
            df = pd.DataFrame(results_data)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'detection_results_{timestamp}.xlsx'
            filepath = os.path.join(self.export_folder, filename)
            
            # 导出到Excel
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 主要结果表
                df.to_excel(writer, sheet_name='检测结果', index=False)
                
                # 统计信息表
                stats_data = self._generate_statistics(results_data)
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)
                
                # NSFW结果表
                nsfw_data = [item for item in results_data if item['是否NSFW'] == '是']
                if nsfw_data:
                    nsfw_df = pd.DataFrame(nsfw_data)
                    nsfw_df.to_excel(writer, sheet_name='NSFW内容', index=False)
            
            return {
                'success': True,
                'message': '导出成功',
                'filename': filename,
                'filepath': filepath,
                'record_count': len(results_data)
            }
            
        except Exception as e:
            logger.error(f"Excel导出失败: {e}")
            return {
                'success': False,
                'message': f'导出失败: {str(e)}'
            }
    
    def export_detection_results_to_html(self, filters: Dict = None) -> Dict:
        """导出检测结果到HTML"""
        try:
            # 获取检测结果数据
            results_data = self._get_detection_results_data(filters)
            
            if not results_data:
                return {
                    'success': False,
                    'message': '没有找到符合条件的检测结果'
                }
            
            # 生成HTML内容
            html_content = self._generate_html_report(results_data)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'detection_report_{timestamp}.html'
            filepath = os.path.join(self.export_folder, filename)
            
            # 写入HTML文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return {
                'success': True,
                'message': '导出成功',
                'filename': filename,
                'filepath': filepath,
                'record_count': len(results_data)
            }
            
        except Exception as e:
            logger.error(f"HTML导出失败: {e}")
            return {
                'success': False,
                'message': f'导出失败: {str(e)}'
            }
    
    def _get_detection_results_data(self, filters: Dict = None) -> List[Dict]:
        """获取检测结果数据"""
        try:
            # 构建查询
            query = DetectionResult.query.join(CrawlResult).join(Website)
            
            # 应用过滤条件
            if filters:
                if filters.get('is_nsfw') is not None:
                    query = query.filter(DetectionResult.is_nsfw == filters['is_nsfw'])
                
                if filters.get('content_type'):
                    query = query.filter(DetectionResult.content_type == filters['content_type'])
                
                if filters.get('detection_method'):
                    query = query.filter(DetectionResult.detection_method == filters['detection_method'])
                
                if filters.get('start_date'):
                    query = query.filter(DetectionResult.detected_at >= filters['start_date'])
                
                if filters.get('end_date'):
                    query = query.filter(DetectionResult.detected_at <= filters['end_date'])
            
            # 执行查询
            results = query.order_by(DetectionResult.detected_at.desc()).all()
            
            # 转换为字典格式
            data = []
            for result in results:
                crawl_result = result.crawl_result
                website = crawl_result.website
                
                # 解析匹配的关键词
                matched_keywords = []
                if result.matched_keywords:
                    try:
                        import json
                        keywords_data = json.loads(result.matched_keywords)
                        matched_keywords = [kw.get('keyword', '') for kw in keywords_data]
                    except:
                        pass
                
                data.append({
                    'ID': result.id,
                    'URL': crawl_result.url,
                    '网站域名': website.domain,
                    '页面标题': crawl_result.title or '',
                    '内容类型': '文本' if result.content_type == 'text' else '图片',
                    '是否NSFW': '是' if result.is_nsfw else '否',
                    '检测方式': '关键词' if result.detection_method == 'keyword' else 'AI模型',
                    '置信度': f"{result.confidence:.2f}" if result.confidence else '',
                    '匹配关键词': ', '.join(matched_keywords),
                    'API提供商': result.api_provider or '',
                    '内容片段': result.content_snippet[:100] if result.content_snippet else '',
                    '检测时间': result.detected_at.strftime('%Y-%m-%d %H:%M:%S') if result.detected_at else '',
                    '爬取深度': crawl_result.depth,
                    '父页面URL': crawl_result.parent_url or ''
                })
            
            return data
            
        except Exception as e:
            logger.error(f"获取检测结果数据失败: {e}")
            return []
    
    def _generate_statistics(self, results_data: List[Dict]) -> List[Dict]:
        """生成统计信息"""
        try:
            total_count = len(results_data)
            nsfw_count = len([item for item in results_data if item['是否NSFW'] == '是'])
            clean_count = total_count - nsfw_count
            
            text_count = len([item for item in results_data if item['内容类型'] == '文本'])
            image_count = len([item for item in results_data if item['内容类型'] == '图片'])
            
            keyword_count = len([item for item in results_data if item['检测方式'] == '关键词'])
            api_count = len([item for item in results_data if item['检测方式'] == 'AI模型'])
            
            # 域名统计
            domain_stats = {}
            for item in results_data:
                domain = item['网站域名']
                if domain not in domain_stats:
                    domain_stats[domain] = {'total': 0, 'nsfw': 0}
                domain_stats[domain]['total'] += 1
                if item['是否NSFW'] == '是':
                    domain_stats[domain]['nsfw'] += 1
            
            stats = [
                {'统计项目': '总检测数量', '数值': total_count, '百分比': '100.00%'},
                {'统计项目': 'NSFW内容数量', '数值': nsfw_count, '百分比': f'{nsfw_count/total_count*100:.2f}%' if total_count > 0 else '0.00%'},
                {'统计项目': '正常内容数量', '数值': clean_count, '百分比': f'{clean_count/total_count*100:.2f}%' if total_count > 0 else '0.00%'},
                {'统计项目': '文本检测数量', '数值': text_count, '百分比': f'{text_count/total_count*100:.2f}%' if total_count > 0 else '0.00%'},
                {'统计项目': '图片检测数量', '数值': image_count, '百分比': f'{image_count/total_count*100:.2f}%' if total_count > 0 else '0.00%'},
                {'统计项目': '关键词检测数量', '数值': keyword_count, '百分比': f'{keyword_count/total_count*100:.2f}%' if total_count > 0 else '0.00%'},
                {'统计项目': 'API检测数量', '数值': api_count, '百分比': f'{api_count/total_count*100:.2f}%' if total_count > 0 else '0.00%'},
            ]
            
            # 添加域名统计
            for domain, counts in sorted(domain_stats.items(), key=lambda x: x[1]['nsfw'], reverse=True)[:10]:
                stats.append({
                    '统计项目': f'域名: {domain}',
                    '数值': f"总数:{counts['total']}, NSFW:{counts['nsfw']}",
                    '百分比': f'{counts["nsfw"]/counts["total"]*100:.2f}%' if counts['total'] > 0 else '0.00%'
                })
            
            return stats
            
        except Exception as e:
            logger.error(f"生成统计信息失败: {e}")
            return []
    
    def _generate_html_report(self, results_data: List[Dict]) -> str:
        """生成HTML报告"""
        try:
            # 生成统计信息
            stats_data = self._generate_statistics(results_data)
            
            # HTML模板
            html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容检测报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        h1, h2 {{ color: #333; }}
        .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }}
        .stat-card {{ background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }}
        .stat-number {{ font-size: 24px; font-weight: bold; color: #007bff; }}
        .stat-label {{ color: #666; margin-top: 5px; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; font-weight: bold; }}
        .nsfw {{ background-color: #ffebee; }}
        .clean {{ background-color: #e8f5e8; }}
        .keyword {{ color: #d32f2f; font-weight: bold; }}
        .api {{ color: #1976d2; font-weight: bold; }}
        .content-snippet {{ max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }}
        .export-info {{ background: #e3f2fd; padding: 15px; border-radius: 6px; margin-bottom: 20px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>内容检测报告</h1>
        
        <div class="export-info">
            <strong>报告生成时间:</strong> {export_time}<br>
            <strong>检测记录总数:</strong> {total_records}<br>
            <strong>NSFW内容数量:</strong> {nsfw_count}<br>
            <strong>检测覆盖率:</strong> {coverage_rate}%
        </div>
        
        <h2>统计概览</h2>
        <div class="stats">
            {stats_cards}
        </div>
        
        <h2>详细检测结果</h2>
        <table>
            <thead>
                <tr>
                    <th>URL</th>
                    <th>域名</th>
                    <th>页面标题</th>
                    <th>内容类型</th>
                    <th>检测结果</th>
                    <th>检测方式</th>
                    <th>匹配关键词</th>
                    <th>置信度</th>
                    <th>内容片段</th>
                    <th>检测时间</th>
                </tr>
            </thead>
            <tbody>
                {results_rows}
            </tbody>
        </table>
    </div>
</body>
</html>
            """
            
            # 生成统计卡片
            stats_cards = ""
            for stat in stats_data[:8]:  # 只显示前8个统计项
                stats_cards += f"""
                <div class="stat-card">
                    <div class="stat-number">{stat['数值']}</div>
                    <div class="stat-label">{stat['统计项目']}</div>
                </div>
                """
            
            # 生成结果行
            results_rows = ""
            for item in results_data[:1000]:  # 限制显示数量
                row_class = "nsfw" if item['是否NSFW'] == '是' else "clean"
                method_class = "keyword" if item['检测方式'] == '关键词' else "api"
                
                results_rows += f"""
                <tr class="{row_class}">
                    <td><a href="{item['URL']}" target="_blank">{item['URL'][:50]}...</a></td>
                    <td>{item['网站域名']}</td>
                    <td>{item['页面标题'][:30]}...</td>
                    <td>{item['内容类型']}</td>
                    <td><strong>{item['是否NSFW']}</strong></td>
                    <td class="{method_class}">{item['检测方式']}</td>
                    <td>{item['匹配关键词'][:50]}...</td>
                    <td>{item['置信度']}</td>
                    <td class="content-snippet">{item['内容片段']}</td>
                    <td>{item['检测时间']}</td>
                </tr>
                """
            
            # 计算统计数据
            total_records = len(results_data)
            nsfw_count = len([item for item in results_data if item['是否NSFW'] == '是'])
            coverage_rate = round(nsfw_count / total_records * 100, 2) if total_records > 0 else 0
            
            # 填充模板
            html_content = html_template.format(
                export_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                total_records=total_records,
                nsfw_count=nsfw_count,
                coverage_rate=coverage_rate,
                stats_cards=stats_cards,
                results_rows=results_rows
            )
            
            return html_content
            
        except Exception as e:
            logger.error(f"生成HTML报告失败: {e}")
            return f"<html><body><h1>报告生成失败</h1><p>{str(e)}</p></body></html>"