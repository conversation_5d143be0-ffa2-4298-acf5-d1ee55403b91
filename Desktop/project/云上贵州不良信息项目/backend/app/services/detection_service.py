"""
内容检测服务
处理NSFW内容检测相关业务逻辑
"""

import re
import logging
from typing import List, Dict, Tuple
from flask import current_app

from app.extensions import db
from app.models import CrawlResult, DetectionResult, Keyword

logger = logging.getLogger(__name__)

class DetectionService:
    """内容检测服务"""
    
    def __init__(self):
        self.keywords = []
        self.load_keywords()
        
    def load_keywords(self):
        """加载关键词列表"""
        try:
            keywords = Keyword.query.filter_by(is_active=True).all()
            self.keywords = [(kw.word, kw.category) for kw in keywords]
            logger.info(f"加载了 {len(self.keywords)} 个关键词")
        except Exception as e:
            logger.error(f"加载关键词失败: {e}")
            self.keywords = []
            
    def detect_keywords(self, text: str) -> List[Tuple[str, str]]:
        """检测文本中的关键词"""
        found_keywords = []
        text_lower = text.lower()
        
        for keyword, category in self.keywords:
            if keyword.lower() in text_lower:
                found_keywords.append((keyword, category))
                
        return found_keywords
        
    def detect_content(self, crawl_result_id: int) -> DetectionResult:
        """检测爬取结果内容"""
        crawl_result = CrawlResult.query.get(crawl_result_id)
        if not crawl_result:
            raise ValueError(f"爬取结果不存在: {crawl_result_id}")
            
        # 合并标题和内容进行检测
        full_text = f"{crawl_result.title or ''} {crawl_result.content_text or ''}"
        
        # 创建检测结果记录
        detection = DetectionResult(
            crawl_result_id=crawl_result_id,
            detection_method='keyword',  # 先使用关键词检测
            content_type='text',
            content_snippet=full_text[:500] if full_text else ''
        )
        
        # 关键词检测
        found_keywords = self.detect_keywords(full_text)
        
        if found_keywords:
            detection.is_nsfw = True
            detection.confidence_score = 1.0
            detection.detected_keywords = ', '.join([kw[0] for kw in found_keywords])
            detection.keyword_categories = ', '.join(list(set([kw[1] for kw in found_keywords])))
            logger.info(f"检测到关键词: {detection.detected_keywords}")
        else:
            # 如果没有关键词，可以调用第三方API
            # TODO: 集成第三方API
            detection.is_nsfw = False
            detection.confidence_score = 0.0
            
        # 更新爬取结果的NSFW状态
        crawl_result.is_nsfw = detection.is_nsfw
        
        db.session.add(detection)
        db.session.commit()
        
        return detection
        
    def detect_batch(self, crawl_result_ids: List[int]):
        """批量检测"""
        results = []
        for result_id in crawl_result_ids:
            try:
                detection = self.detect_content(result_id)
                results.append(detection)
            except Exception as e:
                logger.error(f"检测失败 {result_id}: {e}")
        
        return results


class FilterService:
    """内容过滤服务"""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
            
        # 移除多余空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fa5.,!?;:，。！？；：]', '', text)
        
        # 移除过长的连续字符
        text = re.sub(r'(.)\1{5,}', r'\1\1\1', text)
        
        return text.strip()
        
    @staticmethod
    def should_filter_image(image_url: str, width: int = 0, height: int = 0) -> bool:
        """判断是否应该过滤图片"""
        # 过滤小图片
        min_width = 100
        min_height = 100
        
        if width and height:
            if width < min_width or height < min_height:
                return True
                
        # 过滤特定类型
        filtered_extensions = ['.gif', '.svg', '.ico']
        for ext in filtered_extensions:
            if image_url.lower().endswith(ext):
                return True
                
        return False