"""
爬虫服务模块
"""
import logging

logger = logging.getLogger(__name__)

class CrawlerService:
    """爬虫服务类"""
    
    def __init__(self):
        self.is_running = False

class CrawlerManager:
    """爬虫管理器"""
    
    def __init__(self):
        self.services = {}
    
    def get_service(self, task_id):
        """获取服务"""
        if task_id not in self.services:
            self.services[task_id] = CrawlerService()
        return self.services[task_id]

crawler_manager = CrawlerManager()
