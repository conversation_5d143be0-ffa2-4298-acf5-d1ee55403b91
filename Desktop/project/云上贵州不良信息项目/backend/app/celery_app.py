#!/usr/bin/env python3
"""
Celery应用配置 - 用于异步执行crawl4ai任务
"""
from celery import Celery
import os

def make_celery(app=None):
    """创建Celery实例"""
    celery = Celery(
        'crawler_tasks',
        broker=os.environ.get('REDIS_URL', 'redis://localhost:6379/0'),
        backend=os.environ.get('REDIS_URL', 'redis://localhost:6379/0'),
        include=['app.tasks.crawler_tasks']
    )
    
    celery.conf.update(
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='UTC',
        enable_utc=True,
        result_expires=3600,
    )
    
    if app:
        # 将Flask应用上下文绑定到Celery任务
        class ContextTask(celery.Task):
            def __call__(self, *args, **kwargs):
                with app.app_context():
                    return self.run(*args, **kwargs)
        
        celery.Task = ContextTask
    
    return celery

# 创建全局Celery实例
celery = make_celery() 