from flask import Flask
from flask_cors import CORS
import os
from typing import Optional

from app.extensions import db, jwt

def create_app(config_name: Optional[str] = None) -> Flask:
    app = Flask(__name__)
    
    # 配置加载
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    from config import config
    app.config.from_object(config[config_name])
    
    # 确保上传和导出目录存在
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['EXPORT_FOLDER'], exist_ok=True)
    
    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    CORS(app, 
         origins=['http://localhost:3000'], 
         supports_credentials=True,
         allow_headers=['Content-Type', 'Authorization'],
         methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])
    
    # 初始化Celery
    from app.celery_app import make_celery
    celery = make_celery(app)
    # 通过setattr为Flask实例动态添加celery属性，避免类型检查报错
    setattr(app, 'celery', celery)

    # 注册蓝图
    from app.routes.auth import auth_bp
    from app.routes.url_management import url_bp
    from app.routes.crawler_control import crawler_bp
    from app.routes.detection import detection_bp
    from app.routes.export import export_bp
    
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(url_bp, url_prefix='/api/urls')
    app.register_blueprint(crawler_bp, url_prefix='/api/crawler')
    app.register_blueprint(detection_bp, url_prefix='/api/detection')
    app.register_blueprint(export_bp, url_prefix='/api/export')
    
    # 导入新的模型
    from app.models.task_urls import TaskUrl
    from app.models.page_image import PageImage
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
    
    @app.route('/api/health')
    def health_check():
        return {'status': 'healthy', 'message': '内容监测系统运行正常'}
    
    return app

# 明确导出
__all__ = ['db', 'jwt', 'create_app'] 