from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.extensions import db
from app.models import DetectionResult, CrawlResult, Keyword
from app.services.detection_service import DetectionService

detection_bp = Blueprint('detection', __name__)

@detection_bp.route('/keywords', methods=['GET'])
@jwt_required()
def list_keywords():
    """获取关键词列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        category = request.args.get('category', '').strip()
        
        query = Keyword.query
        
        if category:
            query = query.filter_by(category=category)
            
        query = query.order_by(Keyword.created_at.desc())
        
        keywords = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'keywords': [kw.to_dict() for kw in keywords.items],
            'total': keywords.total,
            'pages': keywords.pages,
            'current_page': page
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取关键词列表失败: {str(e)}'}), 500

@detection_bp.route('/keywords', methods=['POST'])
@jwt_required()
def add_keyword():
    """添加关键词"""
    try:
        data = request.get_json()
        
        if not data or not data.get('keyword'):
            return jsonify({'error': '关键词不能为空'}), 400
            
        keyword = Keyword(
            word=data['keyword'].strip(),
            category=data.get('category', 'general')
        )
        
        db.session.add(keyword)
        db.session.commit()
        
        # 重新加载关键词
        detection_service = DetectionService()
        detection_service.load_keywords()
        
        return jsonify({
            'message': '关键词添加成功',
            'keyword': keyword.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'添加关键词失败: {str(e)}'}), 500

@detection_bp.route('/results', methods=['GET'])
@jwt_required()
def list_detection_results():
    """获取检测结果列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        is_nsfw = request.args.get('is_nsfw', None)
        
        query = DetectionResult.query
        
        if is_nsfw is not None:
            query = query.filter_by(is_nsfw=bool(is_nsfw))
            
        query = query.order_by(DetectionResult.detected_at.desc())
        
        results = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'results': [result.to_dict() for result in results.items],
            'total': results.total,
            'pages': results.pages,
            'current_page': page
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取检测结果失败: {str(e)}'}), 500

@detection_bp.route('/detect/<int:crawl_result_id>', methods=['POST'])
@jwt_required()
def detect_content(crawl_result_id):
    """手动触发内容检测"""
    try:
        crawl_result = CrawlResult.query.get(crawl_result_id)
        if not crawl_result:
            return jsonify({'error': '爬取结果不存在'}), 404
            
        detection_service = DetectionService()
        detection_result = detection_service.detect_content(crawl_result_id)
        
        return jsonify({
            'message': '检测完成',
            'result': detection_result.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'内容检测失败: {str(e)}'}), 500

@detection_bp.route('/config', methods=['GET'])
@jwt_required()
def get_detection_config():
    """获取检测配置"""
    try:
        from flask import current_app
        
        config = {
            'api_enabled': bool(current_app.config.get('CONTENT_API_URL')),
            'api_url': current_app.config.get('CONTENT_API_URL', ''),
            'keywords_count': Keyword.query.filter_by(is_active=True).count(),
            'filter_settings': {
                'min_image_width': 100,
                'min_image_height': 100,
                'filtered_extensions': ['.gif', '.svg', '.ico']
            }
        }
        
        return jsonify(config), 200
        
    except Exception as e:
        return jsonify({'error': f'获取配置失败: {str(e)}'}), 500 