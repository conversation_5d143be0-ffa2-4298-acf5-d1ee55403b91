from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
import pandas as pd
import uuid
import os
from app.extensions import db
from app.models import Website
from urllib.parse import urlparse

url_bp = Blueprint('urls', __name__)

@url_bp.route('/upload', methods=['POST'])
@jwt_required()
def upload_excel():
    """批量导入URL Excel文件"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if not file.filename or not file.filename.lower().endswith(('.xlsx', '.xls')):
            return jsonify({'error': '只支持Excel文件(.xlsx, .xls)'}), 400
        
        # 保存文件
        filename = secure_filename(file.filename or 'upload.xlsx')
        batch_id = str(uuid.uuid4())
        upload_path = os.path.join(current_app.config['UPLOAD_FOLDER'], f"{batch_id}_{filename}")
        file.save(upload_path)
        
        # 读取Excel文件
        try:
            df = pd.read_excel(upload_path)
        except Exception as e:
            os.remove(upload_path)  # 删除无效文件
            return jsonify({'error': f'读取Excel文件失败: {str(e)}'}), 400
        
        # 验证列名 - 支持多种URL列名
        url_column_names = ['url', 'URL', '网址', '网址(必填)', '链接', '网站地址', 'link', 'LINK', 'Link']
        url_column = None
        
        for col_name in url_column_names:
            if col_name in df.columns:
                url_column = col_name
                break
                
        if url_column is None:
            os.remove(upload_path)
            available_columns = ', '.join(df.columns.tolist())
            return jsonify({
                'error': f'Excel文件必须包含URL列。支持的列名: {", ".join(url_column_names)}。当前文件的列名: {available_columns}'
            }), 400
        
        # 统一列名为url
        df = df.rename(columns={url_column: 'url'})
        
        # 处理URL数据
        urls = df['url'].dropna().unique()
        imported_count = 0
        duplicate_count = 0
        invalid_count = 0
        
        for url in urls:
            url_str = str(url).strip()
            
            # 验证并修复URL格式
            fixed_url = _is_valid_url(url_str)
            if not fixed_url:
                invalid_count += 1
                continue
            
            url_str = fixed_url
            
            # 检查是否已存在
            existing = Website.query.filter_by(url=url_str).first()
            if existing:
                duplicate_count += 1
                continue
            
            # 创建新的网站记录
            website = Website(
                url=url_str,
                import_batch_id=batch_id,
                import_source=filename
            )
            db.session.add(website)
            imported_count += 1
        
        db.session.commit()
        
        # 删除临时文件
        os.remove(upload_path)
        
        return jsonify({
            'message': 'URL导入完成',
            'batch_id': batch_id,
            'statistics': {
                'total_urls': len(urls),
                'imported': imported_count,
                'duplicates': duplicate_count,
                'invalid': invalid_count
            }
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'导入失败: {str(e)}'}), 500

@url_bp.route('/', methods=['GET', 'POST'])
@jwt_required()
def list_websites():
    """获取网站列表或添加单个URL"""
    if request.method == 'POST':
        # POST请求：添加单个URL
        return add_single_url()
    
    # GET请求：获取网站列表
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 100, type=int)  # 默认每页100个
        get_all = request.args.get('all', 'false').lower() == 'true'  # 支持获取所有URL
        
        # 如果请求获取所有URL，不限制数量；否则限制最大值防止性能问题
        if not get_all:
            per_page = min(per_page, 1000)
        domain = request.args.get('domain', '').strip()
        status = request.args.get('status', '').strip()
        batch_id = request.args.get('batch_id', '').strip()
        
        query = Website.query
        
        # 按域名筛选
        if domain:
            query = query.filter(Website.domain.contains(domain))
        
        # 按状态筛选
        if status:
            query = query.filter_by(status=status)
        
        # 按批次筛选
        if batch_id:
            query = query.filter_by(import_batch_id=batch_id)
        
        # 按创建时间倒序排列
        query = query.order_by(Website.created_at.desc())
        
        if get_all:
            # 获取所有URL，不分页
            all_websites = query.all()
            return jsonify({
                'websites': [website.to_dict() for website in all_websites],
                'total': len(all_websites),
                'pages': 1,
                'current_page': 1,
                'all_loaded': True
            }), 200
        else:
            # 正常分页
            websites = query.paginate(
                page=page, per_page=per_page, error_out=False
            )
            
            return jsonify({
                'websites': [website.to_dict() for website in websites.items],
                'total': websites.total,
                'pages': websites.pages,
                'current_page': page,
                'all_loaded': False
            }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取网站列表失败: {str(e)}'}), 500

@url_bp.route('/<int:website_id>', methods=['GET'])
@jwt_required()
def get_website(website_id):
    """获取单个网站详情"""
    try:
        website = Website.query.get(website_id)
        if not website:
            return jsonify({'error': '网站不存在'}), 404
        
        return jsonify({
            'website': website.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取网站详情失败: {str(e)}'}), 500

@url_bp.route('/<int:website_id>', methods=['PUT'])
@jwt_required()
def update_website(website_id):
    """更新网站信息"""
    try:
        website = Website.query.get(website_id)
        if not website:
            return jsonify({'error': '网站不存在'}), 404
        
        data = request.get_json()
        
        if 'url' in data:
            new_url = data['url'].strip()
            if not _is_valid_url(new_url):
                return jsonify({'error': '无效的URL格式'}), 400
            
            # 检查新URL是否已存在（排除当前记录）
            existing = Website.query.filter(
                Website.url == new_url,
                Website.id != website_id
            ).first()
            if existing:
                return jsonify({'error': 'URL已存在'}), 400
            
            website.url = new_url
            website.domain = Website.extract_domain(new_url)
        
        if 'is_active' in data:
            website.is_active = bool(data['is_active'])
        
        db.session.commit()
        
        return jsonify({
            'message': '网站信息更新成功',
            'website': website.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'更新网站信息失败: {str(e)}'}), 500

@url_bp.route('/<int:website_id>', methods=['DELETE'])
@jwt_required()
def delete_website(website_id):
    """删除网站"""
    try:
        website = Website.query.get(website_id)
        if not website:
            return jsonify({'error': '网站不存在'}), 404
        
        db.session.delete(website)
        db.session.commit()
        
        return jsonify({
            'message': '网站删除成功'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'删除网站失败: {str(e)}'}), 500

@url_bp.route('/batches', methods=['GET'])
@jwt_required()
def list_import_batches():
    """获取导入批次列表"""
    try:
        batches = db.session.query(  # type: ignore
            Website.import_batch_id,  # type: ignore
            Website.import_source,  # type: ignore
            db.func.count(Website.id).label('url_count'),
            db.func.min(Website.created_at).label('created_at')
        ).filter(
            Website.import_batch_id.is_not(None)  # type: ignore
        ).group_by(
            Website.import_batch_id, Website.import_source
        ).order_by(
            db.func.min(Website.created_at).desc()
        ).all()
        
        batch_list = []
        for batch in batches:
            batch_list.append({
                'batch_id': batch.import_batch_id,
                'source': batch.import_source,
                'url_count': batch.url_count,
                'created_at': batch.created_at.isoformat() if batch.created_at else None
            })
        
        return jsonify({
            'batches': batch_list
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取导入批次失败: {str(e)}'}), 500

@url_bp.route('/add', methods=['POST'])
@jwt_required()
def add_single_url():
    """添加单个URL"""
    try:
        data = request.get_json()
        
        if not data or not data.get('url'):
            return jsonify({'error': 'URL不能为空'}), 400
            
        url = data['url'].strip()
        
        # 验证URL格式
        try:
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                return jsonify({'error': '无效的URL格式'}), 400
        except Exception:
            return jsonify({'error': '无效的URL格式'}), 400
            
        # 检查URL是否已存在
        existing_website = Website.query.filter_by(url=url).first()
        if existing_website:
            return jsonify({'error': '该URL已存在'}), 409
            
        # 创建新的Website记录
        website = Website(url=url)
        website.domain = parsed_url.netloc
        
        db.session.add(website)
        db.session.commit()
        
        return jsonify({
            'message': 'URL添加成功',
            'website': website.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'添加URL失败: {str(e)}'}), 500



def _is_valid_url(url_str):
    """验证URL格式并自动修复"""
    if not url_str or not isinstance(url_str, str):
        return None
        
    url_str = url_str.strip()
    if not url_str:
        return None
    
    # 自动添加协议前缀
    if not url_str.startswith(('http://', 'https://')):
        url_str = 'http://' + url_str
    
    try:
        parsed = urlparse(url_str)
        # 检查是否有域名
        if not parsed.netloc:
            return None
        # 基本格式检查
        if '.' not in parsed.netloc:
            return None
        return url_str
    except:
        return None 