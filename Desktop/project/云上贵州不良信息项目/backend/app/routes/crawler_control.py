from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime
from app.extensions import db
from app.models import CrawlTask, Website, CrawlResult
from app.models.task_urls import TaskUrl
from app.services.crawler_service import CrawlerManager
import os

crawler_bp = Blueprint('crawler', __name__)

@crawler_bp.route('/tasks', methods=['POST'])
@jwt_required()
def create_crawl_task():
    """创建爬虫任务"""
    try:
        user_id = int(get_jwt_identity())
        data = request.get_json()
        
        if not data or not data.get('name'):
            return jsonify({'error': '任务名称不能为空'}), 400
        
        # 获取要爬取的URL列表
        selected_urls = data.get('selected_urls', [])  # URL ID列表
        if not selected_urls:
            return jsonify({'error': '请至少选择一个URL进行爬取'}), 400
        
        # 验证选择的URL是否存在
        websites = Website.query.filter(Website.id.in_(selected_urls), Website.is_active == True).all()
        if len(websites) != len(selected_urls):
            return jsonify({'error': '部分选择的URL不存在或已禁用'}), 400
        
        # 创建爬虫任务
        task = CrawlTask(
            name=data['name'],
            user_id=user_id,
            description=data.get('description', ''),
            max_threads=data.get('max_threads', 4),
            max_depth=data.get('max_depth', 2),
            request_delay=data.get('request_delay', 1.0),
            enable_js=data.get('enable_js', False),
            same_domain_only=data.get('same_domain_only', True),
            lightweight_mode=data.get('lightweight_mode', False)
        )
        task.total_urls = len(websites)  # 设置总URL数
        
        db.session.add(task)
        db.session.flush()  # 获取任务ID
        
        # 建立任务和URL的关联
        for website in websites:
            task_url = TaskUrl(task_id=task.id, website_id=website.id)
            db.session.add(task_url)
        
        db.session.commit()
        
        return jsonify({
            'message': '爬虫任务创建成功',
            'task': task.to_dict(),
            'assigned_urls': len(websites)
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'创建任务失败: {str(e)}'}), 500

@crawler_bp.route('/tasks', methods=['GET'])
@jwt_required()
def list_crawl_tasks():
    """获取爬虫任务列表"""
    try:
        user_id = int(get_jwt_identity())
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status', '').strip()

        query = CrawlTask.query.filter_by(user_id=user_id)
        
        # Filter by status
        if status:
            query = query.filter_by(status=status)
        
        # Order by creation time descending
        query = query.order_by(CrawlTask.created_at.desc())
        
        tasks = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'tasks': [task.to_dict() for task in tasks.items],
            'total': tasks.total,
            'pages': tasks.pages,
            'current_page': page
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取任务列表失败: {str(e)}'}), 500

@crawler_bp.route('/tasks/<task_id>/start', methods=['POST'])
@jwt_required()
def start_crawl_task(task_id):
    """启动爬虫任务"""
    try:
        user_id = int(get_jwt_identity())
        task = CrawlTask.query.filter_by(task_id=task_id, user_id=user_id).first()
        
        if not task:
            return jsonify({'error': '任务不存在'}), 404
        
        if task.status not in ['pending', 'paused', 'stopped']:
            return jsonify({'error': f'任务状态为{task.status}，无法启动'}), 400
        
        # 检查任务是否有分配的URL
        task_websites = task.websites
        if not task_websites:
            return jsonify({'error': '该任务没有分配的URL，请重新配置任务'}), 400
        
        # 使用Celery异步任务执行爬虫
        try:
            from app.tasks.crawler_tasks import crawl_with_crawl4ai
            
            # 更新任务状态为运行中
            task.status = 'running'
            task.started_at = datetime.utcnow()
            db.session.commit()
            
            print(f"🚀 启动Celery爬虫任务: {task.name} (任务ID: {task.task_id})")
            
            # 使用Celery异步执行爬虫任务
            celery_result = crawl_with_crawl4ai.delay(task.task_id)
            
            print(f"✅ Celery任务已提交: {celery_result.id}")
            
            return jsonify({
                'message': f'爬虫任务已启动，任务ID: {task.task_id}',
                'task_id': task.task_id,
                'celery_task_id': celery_result.id,
                'status': 'running',
                'engine': 'crawl4ai',
                'js_support': True,
                'method': 'celery',
                'note': '任务正在Celery后台执行，请通过任务详情API查看进度'
            }), 200
        
        except Exception as main_e:
            # 处理整个爬虫任务的异常
            print(f"💥 爬虫任务异常: {main_e}")
            task.status = 'failed'
            task.completed_at = datetime.utcnow()
            db.session.commit()
            
            return jsonify({
                'error': f'爬虫任务执行失败: {str(main_e)}',
                'task_id': task.task_id,
                'status': 'failed'
            }), 500
        
    except Exception as e:
        return jsonify({'error': f'启动任务失败: {str(e)}'}), 500

@crawler_bp.route('/tasks/<task_id>/pause', methods=['POST'])
@jwt_required()
def pause_crawl_task(task_id):
    """暂停爬虫任务"""
    try:
        user_id = int(get_jwt_identity())
        task = CrawlTask.query.filter_by(task_id=task_id, user_id=user_id).first()
        
        if not task:
            return jsonify({'error': '任务不存在'}), 404
        
        if task.status not in ['running']:
            return jsonify({'error': '只能暂停运行中的任务'}), 400
        # 直接更新任务状态为暂停，不依赖 CrawlerManager
        try:
            task.status = 'paused'
            task.completed_at = datetime.utcnow()
            db.session.commit()
            return jsonify({
                'message': '爬虫任务已暂停',
                'task': task.to_dict()
            }), 200
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'暂停任务异常: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'暂停任务失败: {str(e)}'}), 500

@crawler_bp.route('/tasks/<task_id>/stop', methods=['POST'])
@jwt_required()
def stop_crawl_task(task_id):
    """停止爬虫任务"""
    try:
        user_id = int(get_jwt_identity())
        task = CrawlTask.query.filter_by(task_id=task_id, user_id=user_id).first()
        
        if not task:
            return jsonify({'error': '任务不存在'}), 404
        
        if task.status not in ['running', 'paused', 'pending']:
            return jsonify({'error': '只能停止运行中、暂停或待执行的任务'}), 400
        
        # 直接强制停止 - 不依赖CrawlerManager
        try:
            # 更新任务状态为停止
            task.status = 'stopped'
            task.completed_at = datetime.utcnow()
            
            # 更新进度为当前状态
            if task.crawled_urls is None:
                task.crawled_urls = 0
            if task.failed_urls is None:
                task.failed_urls = 0
            if task.total_urls and task.total_urls > 0:
                task.progress = (task.crawled_urls / task.total_urls) * 100
            
            db.session.commit()
            
            print(f"✅ 强制停止任务: {task.name}")
            
            return jsonify({
                'message': '爬虫任务已强制停止',
                'task': task.to_dict()
            }), 200
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 停止任务异常: {str(e)}")
            return jsonify({'error': f'任务停止异常: {str(e)}'}), 500
        
    except Exception as e:
        return jsonify({'error': f'停止任务失败: {str(e)}'}), 500

@crawler_bp.route('/tasks/<task_id>', methods=['GET'])
@jwt_required()
def get_crawl_task_detail(task_id):
    """获取爬虫任务详细信息"""
    try:
        user_id = int(get_jwt_identity())
        task = CrawlTask.query.filter_by(task_id=task_id, user_id=user_id).first()
        
        if not task:
            return jsonify({'error': '任务不存在'}), 404
        
        # 获取基本任务信息
        task_info = task.to_dict()
        
        # 获取最近抓取的URL（最新的5个）
        from app.models.crawl_result import CrawlResult
        recent_results = CrawlResult.query.filter_by(crawl_task_id=task.id)\
            .order_by(CrawlResult.crawled_at.desc())\
            .limit(5).all()
        
        recent_urls = []
        for result in recent_results:
            recent_urls.append({
                'url': result.url,
                'status': result.status,
                'crawled_at': result.crawled_at.isoformat() if result.crawled_at else None,
                'response_code': result.response_code,
                'title': result.title[:50] + '...' if result.title and len(result.title) > 50 else result.title
            })
        
        # 获取任务关联的URL总数
        task_websites = task.websites
        assigned_urls = [{'id': w.id, 'url': w.url, 'domain': w.domain} for w in task_websites]
        
        # 增强的任务信息
        task_info.update({
            'recent_crawled_urls': recent_urls,
            'assigned_urls_count': len(assigned_urls),
            'assigned_urls': assigned_urls[:10],  # 只返回前10个URL作为样例
            'current_status_detail': {
                'crawled_count': task.crawled_urls or 0,
                'failed_count': task.failed_urls or 0,
                'remaining_count': (task.total_urls or 0) - (task.crawled_urls or 0) - (task.failed_urls or 0),
                'success_rate': round((task.crawled_urls or 0) / max(task.total_urls or 1, 1) * 100, 2)
            }
        })
        
        # 确保基本数据不为null
        if task_info.get('total_urls') is None:
            task_info['total_urls'] = len(assigned_urls)
        if task_info.get('crawled_urls') is None:
            task_info['crawled_urls'] = task.crawled_urls or 0
        if task_info.get('failed_urls') is None:
            task_info['failed_urls'] = task.failed_urls or 0
        
        return jsonify({
            'task': task_info
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取任务详情失败: {str(e)}'}), 500

@crawler_bp.route('/tasks/<task_id>/test-sync', methods=['POST'])
@jwt_required()
def test_sync_crawl(task_id):
    """测试同步爬虫 - 直接在路由中执行爬取"""
    try:
        user_id = int(get_jwt_identity())
        task = CrawlTask.query.filter_by(task_id=task_id, user_id=user_id).first()
        
        if not task:
            return jsonify({'error': '任务不存在'}), 404
        
        # 获取任务的URL
        task_websites = task.websites
        if not task_websites:
            return jsonify({'error': '该任务没有分配的URL'}), 400
        
        # 同步执行爬虫
        import requests
        from bs4 import BeautifulSoup
        from app.models import CrawlResult
        import time
        
        # 更新任务状态
        task.status = 'running'
        task.started_at = datetime.utcnow()
        db.session.commit()
        
        crawled_count = 0
        failed_count = 0
        results = []
        
        print(f"🚀 开始同步爬取任务: {task.name}")
        
        for i, website in enumerate(task_websites, 1):
            try:
                print(f"🎯 正在爬取 ({i}/{len(task_websites)}): {website.url}")
                
                # 爬取页面
                response = requests.get(website.url, timeout=15)
                response.raise_for_status()
                
                # 解析内容
                soup = BeautifulSoup(response.text, 'html.parser')
                title = soup.title.string if soup.title else ''
                text_content = soup.get_text(strip=True)[:1000]
                
                # 创建爬取结果
                result = CrawlResult(
                    url=website.url,
                    website_id=website.id,
                    crawl_task_id=task.id,
                    depth=0
                )
                result.title = title[:500] if title else ''
                result.content_text = text_content
                result.response_code = response.status_code
                result.status = 'completed'
                result.crawled_at = datetime.utcnow()
                
                db.session.add(result)
                db.session.flush()  # 获取result.id
                
                # 立即进行内容检测
                from app.services.detection_service import DetectionService
                detection_service = DetectionService()
                
                try:
                    detection_result = detection_service.detect_content(result.id)
                    if detection_result.is_nsfw:
                        print(f"🚨 检测到NSFW内容: {detection_result.detected_keywords}")
                        results.append({
                            'url': website.url,
                            'title': title,
                            'status': 'success',
                            'response_code': response.status_code,
                            'nsfw': True,
                            'keywords': detection_result.detected_keywords
                        })
                    else:
                        print(f"✅ 内容正常，无敏感词")
                        results.append({
                            'url': website.url,
                            'title': title,
                            'status': 'success',
                            'response_code': response.status_code,
                            'nsfw': False
                        })
                except Exception as e:
                    print(f"⚠️ 内容检测失败: {e}")
                    results.append({
                        'url': website.url,
                        'title': title,
                        'status': 'success',
                        'response_code': response.status_code,
                        'detection_error': str(e)
                    })
                
                # 实时更新进度
                crawled_count += 1
                task.crawled_urls = crawled_count
                task.progress = (crawled_count / len(task_websites)) * 100
                db.session.commit()
                
                print(f"✅ 成功爬取: {website.url} (进度: {task.progress:.1f}%)")
                
                # 延迟避免过于频繁请求
                if task.request_delay > 0:
                    time.sleep(task.request_delay)
                
            except Exception as e:
                error_type = type(e).__name__
                error_message = str(e)
                print(f"❌ 爬取失败 {website.url}: {error_type}: {error_message}")
                
                # 创建失败的爬取结果记录
                failed_result = CrawlResult(
                    url=website.url,
                    website_id=website.id,
                    crawl_task_id=task.id,
                    depth=0
                )
                failed_result.status = 'failed'
                failed_result.error_message = f"{error_type}: {error_message}"
                failed_result.crawled_at = datetime.utcnow()
                
                # 根据错误类型设置不同的响应码
                if 'ConnectionError' in error_type:
                    failed_result.response_code = 0  # 连接失败
                elif 'Timeout' in error_type:
                    failed_result.response_code = 408  # 请求超时
                elif 'HTTPError' in error_type:
                    # 尝试从异常中提取状态码
                    try:
                        failed_result.response_code = 500
                    except:
                        failed_result.response_code = 500
                else:
                    failed_result.response_code = 500  # 其他错误
                
                db.session.add(failed_result)
                
                failed_count += 1
                task.failed_urls = failed_count
                db.session.commit()
                
                results.append({
                    'url': website.url,
                    'status': 'failed',
                    'error_type': error_type,
                    'error': error_message,
                    'response_code': failed_result.response_code
                })
        
        # 标记任务完成
        task.status = 'completed'
        task.completed_at = datetime.utcnow()
        db.session.commit()
        
        print(f"🎉 同步爬取完成: 成功 {crawled_count}, 失败 {failed_count}")
        
        return jsonify({
            'message': f'同步爬取完成！成功: {crawled_count}, 失败: {failed_count}',
            'task': task.to_dict(),
            'results': results,
            'summary': {
                'total': len(task_websites),
                'success': crawled_count,
                'failed': failed_count,
                'progress': task.progress
            }
        }), 200
        
    except Exception as e:
        print(f"💥 同步爬取异常: {e}")
        # 更新任务状态为失败
        try:
            task.status = 'failed'
            task.completed_at = datetime.utcnow()
            db.session.commit()
        except:
            pass
        return jsonify({'error': f'同步爬取失败: {str(e)}'}), 500 

@crawler_bp.route('/tasks/<task_id>', methods=['DELETE'])
@jwt_required()
def delete_crawl_task(task_id):
    """删除爬虫任务"""
    try:
        user_id = int(get_jwt_identity())
        task = CrawlTask.query.filter_by(task_id=task_id, user_id=user_id).first()
        
        if not task:
            return jsonify({'error': '任务不存在'}), 404
        
        # 检查是否强制删除
        force_delete = request.args.get('force', 'false').lower() == 'true'
        
        # 如果任务正在运行且不是强制删除，先尝试停止
        if task.status == 'running' and not force_delete:
            try:
                # 自动停止任务
                task.status = 'stopped'
                task.completed_at = datetime.utcnow()
                db.session.commit()
                print(f"🛑 自动停止任务以便删除: {task.name}")
            except Exception as e:
                print(f"⚠️ 自动停止任务失败: {e}，继续强制删除")
        
        task_name = task.name
        task_db_id = task.id
        
        try:
            # 删除相关的检测结果（需要先删除，因为有外键约束）
            from app.models.detection_result import DetectionResult
            from app.models.crawl_result import CrawlResult
            
            # 先获取所有相关的爬取结果ID
            crawl_result_ids = [r.id for r in CrawlResult.query.filter_by(crawl_task_id=task_db_id).all()]
            
            # 删除检测结果
            if crawl_result_ids:
                for result_id in crawl_result_ids:
                    DetectionResult.query.filter_by(crawl_result_id=result_id).delete()
            
            # 删除爬取结果
            CrawlResult.query.filter_by(crawl_task_id=task_db_id).delete()
            
            # 删除任务URL关联
            from app.models.task_urls import TaskUrl
            TaskUrl.query.filter_by(task_id=task_db_id).delete()
            
            # 删除任务本身
            db.session.delete(task)
            db.session.commit()
            
            print(f"🗑️ 成功删除任务: {task_name} (强制: {force_delete})")
            
            return jsonify({
                'message': f'任务 "{task_name}" 已成功删除',
                'deleted_task_id': task_id,
                'force_delete': force_delete
            }), 200
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 删除任务失败: {e}")
            return jsonify({'error': f'删除任务失败: {str(e)}'}), 500
        
    except Exception as e:
        return jsonify({'error': f'删除任务失败: {str(e)}'}), 500 

@crawler_bp.route('/tasks/cleanup', methods=['POST'])
@jwt_required()
def cleanup_tasks():
    """清理异常任务状态"""
    try:
        user_id = int(get_jwt_identity())
        
        # 获取所有running状态但可能已卡死的任务
        stuck_tasks = CrawlTask.query.filter_by(user_id=user_id, status='running').all()
        
        cleaned_count = 0
        for task in stuck_tasks:
            try:
                # 检查任务是否真的在运行（简单检查：运行超过1小时的任务视为卡死）
                if task.started_at:
                    from datetime import timedelta
                    if datetime.utcnow() - task.started_at > timedelta(hours=1):
                        task.status = 'failed'
                        task.completed_at = datetime.utcnow()
                        cleaned_count += 1
                        print(f"🧹 清理卡死任务: {task.name}")
                else:
                    # 没有开始时间的running任务，直接标记为失败
                    task.status = 'failed'
                    task.completed_at = datetime.utcnow()
                    cleaned_count += 1
                    print(f"🧹 清理异常任务: {task.name}")
            except Exception as e:
                print(f"⚠️ 清理任务 {task.name} 失败: {e}")
        
        db.session.commit()
        
        return jsonify({
            'message': f'任务清理完成，共清理 {cleaned_count} 个异常任务',
            'cleaned_count': cleaned_count
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'任务清理失败: {str(e)}'}), 500


@crawler_bp.route('/tasks/<task_id>/force-stop', methods=['POST'])
@jwt_required()
def force_stop_task(task_id):
    """强制停止任务（管理员功能）"""
    try:
        user_id = int(get_jwt_identity())
        task = CrawlTask.query.filter_by(task_id=task_id, user_id=user_id).first()
        
        if not task:
            return jsonify({'error': '任务不存在'}), 404
        
        # 强制停止，不管当前状态
        original_status = task.status
        task.status = 'stopped'
        task.completed_at = datetime.utcnow()
        
        # 确保数据完整性
        if task.crawled_urls is None:
            task.crawled_urls = 0
        if task.failed_urls is None:
            task.failed_urls = 0
            
        db.session.commit()
        
        print(f"🚨 强制停止任务: {task.name} (原状态: {original_status})")
        
        return jsonify({
            'message': f'任务已强制停止 (原状态: {original_status})',
            'task': task.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'强制停止失败: {str(e)}'}), 500


@crawler_bp.route('/results', methods=['GET'])
@jwt_required()
def get_crawl_results():
    """获取爬取结果列表"""
    try:
        user_id = int(get_jwt_identity())
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        task_id = request.args.get('task_id', '', type=str)
        
        # 基础查询：通过用户的任务来筛选结果
        from app.models.crawl_result import CrawlResult
        query = db.session.query(CrawlResult).join(CrawlTask).filter(CrawlTask.user_id == user_id)
        
        # 如果指定了任务ID，则筛选特定任务
        if task_id:
            task = CrawlTask.query.filter_by(task_id=task_id, user_id=user_id).first()
            if task:
                query = query.filter(CrawlResult.crawl_task_id == task.id)
            else:
                return jsonify({'error': '任务不存在'}), 404
        
        # 按爬取时间降序排列
        query = query.order_by(CrawlResult.crawled_at.desc())
        
        # 分页
        results = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # 格式化返回数据
        crawl_results = []
        for result in results.items:
            crawl_results.append({
                'id': result.id,
                'url': result.url,
                'title': result.title,
                'content_text': result.content_text,
                'content_html': result.content_html,
                'status': result.status,
                'response_code': result.response_code,
                'content_type': result.content_type,
                'content_length': result.content_length,
                'images_count': result.images_count,
                'links_count': result.links_count,
                'page_load_time': result.page_load_time,
                'is_nsfw': result.is_nsfw,
                'error_message': result.error_message,
                'crawled_at': result.crawled_at.isoformat() if result.crawled_at else None,
                'depth': result.depth,
                'parent_url': result.parent_url,
                'task_name': result.crawl_task.name if result.crawl_task else None
            })
        
        return jsonify({
            'results': crawl_results,
            'total': results.total,
            'pages': results.pages,
            'current_page': page,
            'per_page': per_page
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取爬取结果失败: {str(e)}'}), 500


@crawler_bp.route('/results/<int:result_id>', methods=['GET'])
@jwt_required()
def get_crawl_result_detail(result_id):
    """获取爬取结果详情"""
    try:
        user_id = int(get_jwt_identity())
        
        # 查询结果并验证权限
        from app.models.crawl_result import CrawlResult
        result = db.session.query(CrawlResult).join(CrawlTask).filter(
            CrawlResult.id == result_id,
            CrawlTask.user_id == user_id
        ).first()
        
        if not result:
            return jsonify({'error': '爬取结果不存在或无权限访问'}), 404
        
        # 获取关联的图片
        from app.models.page_image import PageImage
        images = PageImage.query.filter_by(crawl_result_id=result_id).all()
        
        # 获取检测结果
        from app.models.detection_result import DetectionResult
        detection_results = DetectionResult.query.filter_by(crawl_result_id=result_id).all()
        
        # 格式化返回数据
        result_detail = {
            'id': result.id,
            'url': result.url,
            'title': result.title,
            'content_text': result.content_text,
            'content_html': result.content_html,
            'status': result.status,
            'response_code': result.response_code,
            'content_type': result.content_type,
            'content_length': result.content_length,
            'images_count': result.images_count,
            'links_count': result.links_count,
            'page_load_time': result.page_load_time,
            'is_nsfw': result.is_nsfw,
            'error_message': result.error_message,
            'crawled_at': result.crawled_at.isoformat() if result.crawled_at else None,
            'depth': result.depth,
            'parent_url': result.parent_url,
            'task_name': result.crawl_task.name if result.crawl_task else None,
            'images': [{
                'id': img.id,
                'url': img.url,
                'alt_text': img.alt_text,
                'width': img.width,
                'height': img.height,
                'file_size': img.file_size,
                'format': img.format,
                'is_valid': img.is_valid
            } for img in images],
            'detection_results': [{
                'id': dr.id,
                'content_type': dr.content_type,
                'content_snippet': dr.content_snippet,
                'detection_method': dr.detection_method,
                'is_nsfw': dr.is_nsfw,
                'confidence_score': dr.confidence,
                'detected_keywords': dr.matched_keywords,
                'keyword_categories': getattr(dr, 'keyword_categories', None),
                'detected_at': dr.detected_at.isoformat() if dr.detected_at else None
            } for dr in detection_results]
        }
        
        return jsonify(result_detail), 200
        
    except Exception as e:
        return jsonify({'error': f'获取爬取结果详情失败: {str(e)}'}), 500


@crawler_bp.route('/results/<int:result_id>/images', methods=['GET'])
@jwt_required()
def get_crawl_result_images(result_id):
    """获取爬取结果的图片列表"""
    try:
        user_id = int(get_jwt_identity())
        
        # 验证结果权限
        from app.models.crawl_result import CrawlResult
        result = db.session.query(CrawlResult).join(CrawlTask).filter(
            CrawlResult.id == result_id,
            CrawlTask.user_id == user_id
        ).first()
        
        if not result:
            return jsonify({'error': '爬取结果不存在或无权限访问'}), 404
        
        # 获取图片列表
        from app.models.page_image import PageImage
        images = PageImage.query.filter_by(crawl_result_id=result_id).all()
        
        images_data = [{
            'id': img.id,
            'url': img.url,
            'alt_text': img.alt_text,
            'width': img.width,
            'height': img.height,
            'file_size': img.file_size,
            'format': img.format,
            'is_valid': img.is_valid
        } for img in images]
        
        return jsonify({
            'result_id': result_id,
            'result_url': result.url,
            'images': images_data,
            'total_images': len(images_data)
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取图片列表失败: {str(e)}'}), 500 