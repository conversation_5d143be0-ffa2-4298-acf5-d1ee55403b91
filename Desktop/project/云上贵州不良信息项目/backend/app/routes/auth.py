from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, create_access_token
from werkzeug.security import check_password_hash
from app.extensions import db
from app.models import User
from datetime import datetime

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400
        
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '')
        
        # 验证必填字段
        if not username or not email or not password:
            return jsonify({'error': '用户名、邮箱和密码不能为空'}), 400
        
        # 验证用户名长度
        if len(username) < 3 or len(username) > 20:
            return jsonify({'error': '用户名长度必须在3-20个字符之间'}), 400
        
        # 验证密码长度  
        if len(password) < 6:
            return jsonify({'error': '密码长度不能少于6个字符'}), 400
        
        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            return jsonify({'error': '用户名已存在'}), 400
        
        # 检查邮箱是否已存在
        if User.query.filter_by(email=email).first():
            return jsonify({'error': '邮箱已被注册'}), 400
        
        # 创建新用户
        user = User(username=username, email=email, password=password)
        db.session.add(user)
        db.session.commit()
        
        # 生成访问令牌
        access_token = create_access_token(identity=str(user.id))
        
        return jsonify({
            'message': '注册成功',
            'access_token': access_token,
            'user': user.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'注册失败: {str(e)}'}), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        print(f"🔍 收到登录请求: {data}")
        
        if not data or not data.get('username') or not data.get('password'):
            print(f"❌ 缺少字段: data={data}")
            return jsonify({'error': '用户名和密码不能为空'}), 400
        
        username = data['username'].strip()
        password = data['password']
        
        # 查找用户（支持用户名或邮箱登录）
        user = User.query.filter(  # type: ignore
            (User.username == username) | (User.email == username.lower())  # type: ignore
        ).first()
        
        if not user:
            return jsonify({'error': '用户不存在'}), 401
        
        if not user.is_active:
            return jsonify({'error': '账户已被禁用'}), 401
        
        password_ok = user.check_password(password)
        print(f"🔐 密码检查: 用户={username}, 密码='{password}', 长度={len(password)}, 结果={password_ok}")
        print(f"🔍 密码字符码: {[f'{c}({ord(c)})' for c in password]}")
        
        if not password_ok:
            return jsonify({'error': '密码错误'}), 401
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        # 生成访问令牌
        access_token = create_access_token(identity=str(user.id))
        
        return jsonify({
            'message': '登录成功',
            'access_token': access_token,
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'登录失败: {str(e)}'}), 500

@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """获取用户信息"""
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': '用户不存在'}), 404
        
        return jsonify({
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取用户信息失败: {str(e)}'}), 500

@auth_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """更新用户信息"""
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': '用户不存在'}), 404
        
        data = request.get_json()
        
        if 'username' in data:
            new_username = data['username'].strip()
            if len(new_username) < 3 or len(new_username) > 20:
                return jsonify({'error': '用户名长度必须在3-20个字符之间'}), 400
            
            # 检查用户名是否被其他用户使用
            existing = User.query.filter(  # type: ignore
                User.username == new_username,  # type: ignore
                User.id != user_id
            ).first()
            if existing:
                return jsonify({'error': '用户名已被使用'}), 400
            
            user.username = new_username
        
        if 'email' in data:
            new_email = data['email'].strip().lower()
            # 检查邮箱是否被其他用户使用
            existing = User.query.filter(  # type: ignore
                User.email == new_email,  # type: ignore
                User.id != user_id
            ).first()
            if existing:
                return jsonify({'error': '邮箱已被使用'}), 400
            
            user.email = new_email
        
        if 'password' in data:
            new_password = data['password']
            if len(new_password) < 6:
                return jsonify({'error': '密码长度不能少于6个字符'}), 400
            user.set_password(new_password)
        
        db.session.commit()
        
        return jsonify({
            'message': '信息更新成功',
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'更新用户信息失败: {str(e)}'}), 500

@auth_bp.route('/users', methods=['GET'])
@jwt_required()
def list_users():
    """获取用户列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        users = User.query.order_by(User.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'users': [user.to_dict() for user in users.items],
            'total': users.total,
            'pages': users.pages,
            'current_page': page
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取用户列表失败: {str(e)}'}), 500

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """用户登出"""
    try:
        # JWT无状态，前端删除token即可
        return jsonify({
            'message': '登出成功'
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'登出失败: {str(e)}'}), 500