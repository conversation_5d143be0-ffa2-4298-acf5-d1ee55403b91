from flask import Blueprint, request, jsonify, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
import pandas as pd
import os
from datetime import datetime
from app.extensions import db
from app.models import DetectionResult, CrawlResult
from io import BytesIO

export_bp = Blueprint('export', __name__)

@export_bp.route('/excel', methods=['POST'])
@jwt_required()
def export_excel():
    """导出检测结果为Excel"""
    try:
        data = request.get_json() or {}
        is_nsfw = data.get('is_nsfw')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        # 构建查询
        query = DetectionResult.query
        
        if is_nsfw is not None:
            query = query.filter_by(is_nsfw=bool(is_nsfw))
            
        if start_date:
            query = query.filter(DetectionResult.detected_at >= start_date)
            
        if end_date:
            query = query.filter(DetectionResult.detected_at <= end_date)
            
        results = query.all()
        
        if not results:
            return jsonify({'error': '没有找到匹配的数据'}), 400
        
        # 构建数据列表
        data_list = []
        for result in results:
            data_list.append({
                'URL': result.crawl_result.url if result.crawl_result else '',
                '标题': result.crawl_result.title if result.crawl_result else '',
                '内容类型': result.content_type,
                'NSFW': '是' if result.is_nsfw else '否',
                '检测方法': result.detection_method,
                '检测时间': result.detected_at.strftime('%Y-%m-%d %H:%M:%S') if result.detected_at else '',
                '置信度': result.confidence_score or 0.0,
                '关键词': result.detected_keywords or '',
                '网站域名': result.crawl_result.website.domain if result.crawl_result and result.crawl_result.website else ''
            })
        
        # 创建DataFrame
        df = pd.DataFrame(data_list)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'detection_results_{timestamp}.xlsx'
        
        # 保存到内存
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='检测结果', index=False)
        
        output.seek(0)
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        return jsonify({'error': f'导出失败: {str(e)}'}), 500

@export_bp.route('/html', methods=['POST'])
@jwt_required()
def export_html():
    """导出检测结果为HTML"""
    try:
        data = request.get_json() or {}
        is_nsfw = data.get('is_nsfw')
        
        # 构建查询
        query = DetectionResult.query
        
        if is_nsfw is not None:
            query = query.filter_by(is_nsfw=bool(is_nsfw))
            
        results = query.limit(1000).all()  # 限制数量
        
        if not results:
            return jsonify({'error': '没有找到匹配的数据'}), 400
        
        # 生成HTML内容
        html_content = generate_html_report(results)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'detection_report_{timestamp}.html'
        
        # 保存到内存
        output = BytesIO()
        output.write(html_content.encode('utf-8'))
        output.seek(0)
        
        return send_file(
            output,
            mimetype='text/html',
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        return jsonify({'error': f'导出失败: {str(e)}'}), 500

@export_bp.route('/summary', methods=['GET'])
@jwt_required()
def export_summary():
    """获取导出数据摘要"""
    try:
        # 统计信息
        total_results = DetectionResult.query.count()
        nsfw_results = DetectionResult.query.filter_by(is_nsfw=True).count()
        
        # 按检测方法统计
        keyword_results = DetectionResult.query.filter_by(detection_method='keyword').count()
        api_results = DetectionResult.query.filter_by(detection_method='api').count()
        
        return jsonify({
            'summary': {
                'total_results': total_results,
                'nsfw_results': nsfw_results,
                'clean_results': total_results - nsfw_results,
                'by_method': {
                    'keyword': keyword_results,
                    'api': api_results
                },
                'nsfw_rate': round((nsfw_results / total_results * 100), 2) if total_results > 0 else 0
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取摘要失败: {str(e)}'}), 500

def generate_html_report(results):
    """生成HTML报告"""
    html = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>内容检测报告</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .header {{ background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
            .summary {{ display: flex; gap: 20px; margin-bottom: 20px; }}
            .summary-item {{ background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 1; }}
            table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            .nsfw {{ background-color: #ffebee; }}
            .clean {{ background-color: #e8f5e8; }}
            .keyword {{ color: #d32f2f; font-weight: bold; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>内容检测报告</h1>
            <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <div class="summary">
            <div class="summary-item">
                <h3>总检测数</h3>
                <p style="font-size: 24px; margin: 0;">{len(results)}</p>
            </div>
            <div class="summary-item">
                <h3>NSFW检测</h3>
                <p style="font-size: 24px; margin: 0; color: #d32f2f;">{sum(1 for r in results if r.is_nsfw)}</p>
            </div>
            <div class="summary-item">
                <h3>正常内容</h3>
                <p style="font-size: 24px; margin: 0; color: #388e3c;">{sum(1 for r in results if not r.is_nsfw)}</p>
            </div>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>URL</th>
                    <th>标题</th>
                    <th>检测结果</th>
                    <th>检测方法</th>
                    <th>关键词</th>
                    <th>检测时间</th>
                </tr>
            </thead>
            <tbody>
    """
    
    for result in results:
        row_class = 'nsfw' if result.is_nsfw else 'clean'
        keywords = result.detected_keywords or ''
        keywords_html = f'<span class="keyword">{keywords}</span>' if keywords else ''
        
        html += f"""
                <tr class="{row_class}">
                    <td>{result.crawl_result.url if result.crawl_result else ''}</td>
                    <td>{result.crawl_result.title if result.crawl_result else ''}</td>
                    <td>{'NSFW' if result.is_nsfw else '正常'}</td>
                    <td>{result.detection_method}</td>
                    <td>{keywords_html}</td>
                    <td>{result.detected_at.strftime('%Y-%m-%d %H:%M:%S') if result.detected_at else ''}</td>
                </tr>
        """
    
    html += """
            </tbody>
        </table>
    </body>
    </html>
    """
    
    return html 