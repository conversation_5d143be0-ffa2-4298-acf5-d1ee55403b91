"""
内容过滤工具
"""

import re
from typing import Dict, List, Optional
from flask import current_app

class ContentFilter:
    """内容过滤器"""
    
    def __init__(self):
        self.config = current_app.config.get('CONTENT_FILTER_CONFIG', {})
        self.min_text_length = self.config.get('min_text_length', 10)
        self.min_image_width = self.config.get('min_image_width', 100)
        self.min_image_height = self.config.get('min_image_height', 100)
        self.filter_patterns = self.config.get('filter_patterns', [])
    
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ''
        
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符和乱码
        cleaned = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cleaned)
        
        # 移除HTML实体
        import html
        cleaned = html.unescape(cleaned)
        
        return cleaned
    
    def should_process_text(self, text: str) -> bool:
        """判断文本是否应该被处理"""
        if not text or len(text.strip()) < self.min_text_length:
            return False
        
        # 检查过滤模式
        for pattern in self.filter_patterns:
            try:
                if re.match(pattern, text, re.IGNORECASE):
                    return False
            except re.error:
                continue
        
        return True
    
    def should_process_image(self, image_data: Dict) -> bool:
        """判断图片是否应该被处理"""
        # 检查尺寸要求
        width = image_data.get('width')
        height = image_data.get('height')
        
        if width and height:
            if width < self.min_image_width or height < self.min_image_height:
                return False
        
        # 检查URL有效性
        url = image_data.get('url', '')
        if not url or not self._is_valid_image_url(url):
            return False
        
        return True
    
    def _is_valid_image_url(self, url: str) -> bool:
        """检查是否为有效的图片URL"""
        if not url:
            return False
        
        # 检查图片扩展名
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'}
        url_lower = url.lower()
        
        # 直接检查扩展名
        if any(url_lower.endswith(ext) for ext in image_extensions):
            return True
        
        # 检查是否包含图片相关参数
        if any(keyword in url_lower for keyword in ['image', 'img', 'photo', 'pic']):
            return True
        
        return False
    
    def extract_meaningful_text(self, html_content: str) -> str:
        """从HTML中提取有意义的文本"""
        try:
            from bs4 import BeautifulSoup
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除脚本和样式标签
            for script in soup(["script", "style", "meta", "link"]):
                script.decompose()
            
            # 提取文本
            text = soup.get_text()
            
            # 清理文本
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return self.clean_text(text)
            
        except Exception:
            return ''
    
    def filter_sensitive_content(self, text: str) -> Dict:
        """过滤敏感内容（基础版本）"""
        # 这里可以集成更复杂的内容过滤逻辑
        # 目前只做基础的长度和格式检查
        
        result = {
            'original_length': len(text),
            'filtered_text': text,
            'is_valid': True,
            'filter_reasons': []
        }
        
        if not self.should_process_text(text):
            result['is_valid'] = False
            result['filter_reasons'].append('文本长度不足或格式不符合要求')
        
        return result