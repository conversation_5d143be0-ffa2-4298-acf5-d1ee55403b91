"""
URL处理工具函数
"""

import re
from urllib.parse import urlparse, urljoin, urlunparse
from typing import Optional

def normalize_url(url: str) -> str:
    """标准化URL"""
    try:
        # 解析URL
        parsed = urlparse(url.strip())
        
        # 确保有协议
        if not parsed.scheme:
            url = 'http://' + url
            parsed = urlparse(url)
        
        # 移除fragment
        normalized = urlunparse((
            parsed.scheme.lower(),
            parsed.netloc.lower(),
            parsed.path,
            parsed.params,
            parsed.query,
            ''  # 移除fragment
        ))
        
        # 移除末尾的斜杠（除非是根路径）
        if normalized.endswith('/') and len(parsed.path) > 1:
            normalized = normalized[:-1]
        
        return normalized
        
    except Exception:
        return url

def is_valid_url(url: str) -> bool:
    """验证URL格式"""
    try:
        if not url or not isinstance(url, str):
            return False
        
        # 基本格式检查
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if not url_pattern.match(url):
            return False
        
        # 使用urlparse进一步验证
        parsed = urlparse(url)
        return all([parsed.scheme, parsed.netloc]) and parsed.scheme in ['http', 'https']
        
    except Exception:
        return False

def extract_domain(url: str) -> str:
    """从URL提取域名"""
    try:
        parsed = urlparse(url)
        domain = parsed.netloc
        
        # 移除www前缀
        if domain.startswith('www.'):
            domain = domain[4:]
        
        # 移除端口号
        if ':' in domain:
            domain = domain.split(':')[0]
        
        return domain
        
    except Exception:
        return url[:100] if url else ''

def is_same_domain(url1: str, url2: str) -> bool:
    """检查两个URL是否属于同一域名"""
    try:
        domain1 = extract_domain(url1)
        domain2 = extract_domain(url2)
        return domain1.lower() == domain2.lower()
    except Exception:
        return False

def get_base_url(url: str) -> str:
    """获取URL的基础部分（协议+域名）"""
    try:
        parsed = urlparse(url)
        return f"{parsed.scheme}://{parsed.netloc}"
    except Exception:
        return url

def join_url(base_url: str, relative_url: str) -> str:
    """安全地连接URL"""
    try:
        return urljoin(base_url, relative_url)
    except Exception:
        return relative_url