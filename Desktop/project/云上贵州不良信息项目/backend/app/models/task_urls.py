from app.extensions import db

class TaskUrl(db.Model):
    """任务和URL的关联表"""
    __tablename__ = 'task_urls'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    task_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON>('crawl_tasks.id'), nullable=False)
    website_id = db.Column(db.Integer, db.Foreign<PERSON>ey('websites.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    
    # 确保同一个任务不会重复添加相同的URL
    __table_args__ = (db.UniqueConstraint('task_id', 'website_id', name='unique_task_website'),)
    
    def to_dict(self):
        return {
            'id': self.id,
            'task_id': self.task_id,
            'website_id': self.website_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        } 