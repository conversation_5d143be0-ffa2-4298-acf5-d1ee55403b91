from app.extensions import db
from datetime import datetime


class PageImage(db.Model):
    """页面图片模型"""
    __tablename__ = 'page_images'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    url = db.Column(db.String(2048), nullable=False)
    alt_text = db.Column(db.String(500), nullable=True)
    width = db.Column(db.Integer, nullable=True)
    height = db.Column(db.Integer, nullable=True)
    file_size = db.Column(db.Integer, nullable=True)
    format = db.Column(db.String(20), nullable=True)
    is_valid = db.Column(db.<PERSON>, nullable=True)
    
    # 外键关联到爬取结果
    crawl_result_id = db.Column(db.Integer, db.<PERSON>ey('crawl_results.id'), nullable=False)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'url': self.url,
            'alt_text': self.alt_text,
            'width': self.width,
            'height': self.height,
            'file_size': self.file_size,
            'format': self.format,
            'is_valid': self.is_valid,
            'crawl_result_id': self.crawl_result_id
        }
    
    def __repr__(self):
        return f'<PageImage {self.id}: {self.url[:50]}>'