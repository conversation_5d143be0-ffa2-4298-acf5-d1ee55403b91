from app.extensions import db
from datetime import datetime

class DetectionResult(db.Model):
    __tablename__ = 'detection_results'
    
    id = db.Column(db.Integer, primary_key=True)
    content_type = db.Column(db.String(20), nullable=False)  # text, image
    content_snippet = db.Column(db.Text)  # 内容片段或图片URL
    
    # 检测结果
    is_nsfw = db.Column(db.<PERSON>, default=False)
    confidence = db.Column(db.Float)  # 置信度 0-1
    detection_method = db.Column(db.String(20), nullable=False)  # keyword, api
    
    # 关键词检测信息
    matched_keywords = db.Column(db.Text)  # JSON格式存储匹配的关键词
    
    # API检测信息  
    api_response = db.Column(db.Text)  # API返回的原始响应
    api_provider = db.Column(db.String(50))  # API提供商
    
    # 高亮信息
    highlighted_content = db.Column(db.Text)  # 高亮后的内容
    
    # 时间信息
    detected_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 外键
    crawl_result_id = db.Column(db.Integer, db.ForeignKey('crawl_results.id'), nullable=False)
    
    def __init__(self, content_type, content_snippet, crawl_result_id, 
                 detection_method, is_nsfw=False, confidence=None):
        self.content_type = content_type
        self.content_snippet = content_snippet
        self.crawl_result_id = crawl_result_id
        self.detection_method = detection_method
        self.is_nsfw = is_nsfw
        self.confidence = confidence
    
    def set_keyword_result(self, is_nsfw, matched_keywords, highlighted_content=None):
        """设置关键词检测结果"""
        self.is_nsfw = is_nsfw
        self.matched_keywords = matched_keywords
        self.highlighted_content = highlighted_content
        self.confidence = 1.0 if is_nsfw else 0.0  # 关键词匹配为确定性结果
        db.session.commit()
    
    def set_api_result(self, is_nsfw, confidence, api_response, api_provider):
        """设置API检测结果"""
        self.is_nsfw = is_nsfw
        self.confidence = confidence
        self.api_response = api_response
        self.api_provider = api_provider
        db.session.commit()
    
    def get_matched_keywords_list(self):
        """获取匹配的关键词列表"""
        if not self.matched_keywords:
            return []
        try:
            import json
            return json.loads(self.matched_keywords)
        except:
            return []
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'content_type': self.content_type,
            'content_snippet': self.content_snippet[:200] if self.content_snippet else None,  # 限制长度
            'is_nsfw': self.is_nsfw,
            'confidence': self.confidence,
            'detection_method': self.detection_method,
            'matched_keywords': self.get_matched_keywords_list(),
            'api_provider': self.api_provider,
            'highlighted_content': self.highlighted_content,
            'detected_at': self.detected_at.isoformat() if self.detected_at else None,
            'crawl_result_id': self.crawl_result_id
        }
    
    def __repr__(self):
        return f'<DetectionResult {self.content_type} NSFW:{self.is_nsfw}>' 