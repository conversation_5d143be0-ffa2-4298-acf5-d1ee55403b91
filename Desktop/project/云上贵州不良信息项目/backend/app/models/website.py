from app.extensions import db
from datetime import datetime
from urllib.parse import urlparse

class Website(db.Model):
    __tablename__ = 'websites'
    
    id = db.Column(db.Integer, primary_key=True)
    url = db.Column(db.String(2048), nullable=False, index=True)
    domain = db.Column(db.String(255), nullable=False, index=True)
    title = db.Column(db.String(500))
    description = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')  # pending, crawling, completed, failed
    is_active = db.Column(db.<PERSON><PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 导入信息
    import_batch_id = db.Column(db.String(50))  # 批次ID，用于标识同一次导入的URL
    import_source = db.Column(db.String(100))  # 导入来源，如Excel文件名
    
    # 关系
    crawl_results = db.relationship('CrawlResult', backref='website', lazy='dynamic', cascade='all, delete-orphan')
    task_urls = db.relationship('TaskUrl', backref='website', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, url, import_batch_id=None, import_source=None):
        self.url = url.strip()
        self.domain = self.extract_domain(url)
        self.import_batch_id = import_batch_id
        self.import_source = import_source
    
    @staticmethod
    def extract_domain(url):
        """从URL提取域名"""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc
            if domain.startswith('www.'):
                domain = domain[4:]
            return domain
        except Exception:
            return url[:100]  # 如果解析失败，返回前100个字符
    
    def update_status(self, status, title=None, description=None):
        """更新网站状态"""
        self.status = status
        if title:
            self.title = title
        if description:
            self.description = description
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    def get_crawl_summary(self):
        """获取爬取摘要信息"""
        total_pages = self.crawl_results.count()
        nsfw_pages = self.crawl_results.filter_by(is_nsfw=True).count()
        return {
            'total_pages': total_pages,
            'nsfw_pages': nsfw_pages,
            'clean_pages': total_pages - nsfw_pages
        }
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'url': self.url,
            'domain': self.domain,
            'title': self.title,
            'description': self.description,
            'status': self.status,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'import_batch_id': self.import_batch_id,
            'import_source': self.import_source,
            'crawl_summary': self.get_crawl_summary()
        }
    
    def __repr__(self):
        return f'<Website {self.domain}>' 