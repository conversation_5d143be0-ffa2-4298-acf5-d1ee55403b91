from app.extensions import db
from datetime import datetime
import uuid

class CrawlTask(db.Model):
    __tablename__ = 'crawl_tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.String(50), unique=True, nullable=False, index=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    
    # 任务状态
    status = db.Column(db.String(20), default='pending')  # pending, running, paused, completed, failed
    progress = db.Column(db.Float, default=0.0)  # 0-100的进度百分比
    current_url = db.Column(db.String(2048))  # 当前正在爬取的URL
    
    # 爬虫配置
    max_threads = db.Column(db.Integer, default=4)
    max_depth = db.Column(db.Integer, default=3)
    request_delay = db.Column(db.Float, default=1.0)  # 请求间隔（秒）
    enable_js = db.Column(db.Boolean, default=True)  # 是否启用JavaScript渲染
    same_domain_only = db.Column(db.Bo<PERSON>, default=True)  # 是否只爬取同域名页面
    lightweight_mode = db.Column(db.Boolean, default=False)  # 轻量级模式：快速爬取，减少内容提取
    
    # 时间信息
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 统计信息
    total_urls = db.Column(db.Integer, default=0)
    crawled_urls = db.Column(db.Integer, default=0)
    failed_urls = db.Column(db.Integer, default=0)
    
    # 外键
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # 关系
    crawl_results = db.relationship('CrawlResult', backref='crawl_task', lazy='dynamic', cascade='all, delete-orphan')
    task_urls = db.relationship('TaskUrl', backref='crawl_task', lazy='dynamic', cascade='all, delete-orphan')
    
    # 通过关联表获取任务的URL
    @property
    def websites(self):
        from app.models.website import Website
        from app.models.task_urls import TaskUrl
        return Website.query.join(TaskUrl).filter(TaskUrl.task_id == self.id).all()
    
    def __init__(self, name, user_id, description=None, max_threads=4, max_depth=3, 
                 request_delay=1.0, enable_js=True, same_domain_only=True, lightweight_mode=False):
        self.task_id = str(uuid.uuid4())
        self.name = name
        self.user_id = user_id
        self.description = description
        self.max_threads = max_threads
        self.max_depth = max_depth
        self.request_delay = request_delay
        self.enable_js = enable_js
        self.same_domain_only = same_domain_only
        self.lightweight_mode = lightweight_mode
    
    def start_task(self):
        """开始任务"""
        self.status = 'running'
        self.started_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    def pause_task(self):
        """暂停任务"""
        self.status = 'paused'
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    def complete_task(self):
        """完成任务"""
        self.status = 'completed'
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        self.progress = 100.0
        db.session.commit()
    
    def fail_task(self, error_message=None):
        """任务失败"""
        self.status = 'failed'
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        if error_message:
            self.description = f"{self.description or ''}\n错误: {error_message}"
        db.session.commit()
    
    def update_progress(self, current_url=None, crawled_count=None):
        """更新进度"""
        if current_url:
            self.current_url = current_url
        if crawled_count is not None:
            self.crawled_urls = crawled_count
            if self.total_urls > 0:
                self.progress = min((crawled_count / self.total_urls) * 100, 100.0)
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    def get_runtime(self):
        """获取运行时长（分钟）"""
        if not self.started_at:
            return 0
        end_time = self.completed_at or datetime.utcnow()
        return (end_time - self.started_at).total_seconds() / 60
    
    def get_statistics(self):
        """获取任务统计信息"""
        from .detection_result import DetectionResult
        nsfw_count = self.crawl_results.join(DetectionResult).filter_by(is_nsfw=True).count()
        return {
            'total_urls': self.total_urls,
            'crawled_urls': self.crawled_urls,
            'failed_urls': self.failed_urls,
            'nsfw_detected': nsfw_count,
            'runtime_minutes': self.get_runtime()
        }
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'name': self.name,
            'description': self.description,
            'status': self.status,
            'max_threads': self.max_threads,
            'max_depth': self.max_depth,
            'request_delay': self.request_delay,
            'enable_js': self.enable_js,
            'same_domain_only': self.same_domain_only,
            'lightweight_mode': self.lightweight_mode,
            'total_urls': self.total_urls or 0,
            'crawled_urls': self.crawled_urls or 0,
            'failed_urls': self.failed_urls or 0,
            'progress': self.progress or 0.0,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'user_id': self.user_id
        }
    
    def __repr__(self):
        return f'<CrawlTask {self.name}>' 