from app.extensions import db
from datetime import datetime

class Keyword(db.Model):
    __tablename__ = 'keywords'
    
    id = db.Column(db.Integer, primary_key=True)
    word = db.Column(db.String(200), nullable=False, index=True)
    category = db.Column(db.String(50))  # 关键词分类，如：暴力、色情、政治等
    description = db.Column(db.Text)
    is_active = db.Column(db.Bo<PERSON>, default=True)
    is_regex = db.Column(db.<PERSON><PERSON><PERSON>, default=False)  # 是否为正则表达式
    case_sensitive = db.Column(db.<PERSON><PERSON>, default=False)  # 是否大小写敏感
    
    # 统计信息
    hit_count = db.Column(db.Integer, default=0)  # 命中次数
    last_hit_at = db.Column(db.DateTime)  # 最后命中时间
    
    # 时间信息
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, word, category=None, description=None, is_regex=False, case_sensitive=False):
        self.word = word.strip()
        self.category = category
        self.description = description
        self.is_regex = is_regex
        self.case_sensitive = case_sensitive
    
    def record_hit(self):
        """记录关键词命中"""
        self.hit_count += 1
        self.last_hit_at = datetime.utcnow()
        db.session.commit()
    
    def get_match_pattern(self):
        """获取匹配模式"""
        if self.is_regex:
            return self.word
        else:
            # 普通关键词，构建单词边界匹配
            import re
            escaped_word = re.escape(self.word)
            return rf'\b{escaped_word}\b'
    
    def test_match(self, text):
        """测试是否匹配给定文本"""
        import re
        try:
            pattern = self.get_match_pattern()
            flags = 0 if self.case_sensitive else re.IGNORECASE
            return bool(re.search(pattern, text, flags))
        except re.error:
            # 正则表达式错误时返回False
            return False
    
    @classmethod
    def get_active_keywords(cls):
        """获取所有活跃的关键词"""
        return cls.query.filter_by(is_active=True).all()
    
    @classmethod
    def search_text_for_keywords(cls, text):
        """在文本中搜索关键词"""
        import re
        matched_keywords = []
        
        keywords = cls.get_active_keywords()
        for keyword in keywords:
            try:
                pattern = keyword.get_match_pattern()
                flags = 0 if keyword.case_sensitive else re.IGNORECASE
                matches = re.finditer(pattern, text, flags)
                
                for match in matches:
                    matched_keywords.append({
                        'keyword': keyword.word,
                        'category': keyword.category,
                        'position': match.span(),
                        'matched_text': match.group()
                    })
                    # 记录命中
                    keyword.record_hit()
                    
            except re.error:
                continue
        
        return matched_keywords
    
    @classmethod
    def highlight_keywords_in_text(cls, text, matched_keywords):
        """在文本中高亮关键词"""
        if not matched_keywords:
            return text
        
        # 按位置排序，从后往前替换避免位置偏移
        sorted_matches = sorted(matched_keywords, key=lambda x: x['position'][0], reverse=True)
        
        highlighted_text = text
        for match in sorted_matches:
            start, end = match['position']
            matched_text = match['matched_text']
            highlighted = f'<mark class="nsfw-keyword" data-category="{match["category"]}">{matched_text}</mark>'
            highlighted_text = highlighted_text[:start] + highlighted + highlighted_text[end:]
        
        return highlighted_text
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'word': self.word,
            'category': self.category,
            'description': self.description,
            'is_active': self.is_active,
            'is_regex': self.is_regex,
            'case_sensitive': self.case_sensitive,
            'hit_count': self.hit_count,
            'last_hit_at': self.last_hit_at.isoformat() if self.last_hit_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<Keyword {self.word}>' 