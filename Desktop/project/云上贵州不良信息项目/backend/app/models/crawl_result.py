from app.extensions import db
from datetime import datetime

class CrawlResult(db.Model):
    __tablename__ = 'crawl_results'
    
    id = db.Column(db.Integer, primary_key=True)
    url = db.Column(db.String(2048), nullable=False, index=True)
    parent_url = db.Column(db.String(2048))  # 父页面URL
    depth = db.Column(db.Integer, default=0)  # 爬取深度
    
    # 页面内容
    title = db.Column(db.String(500))
    content_text = db.Column(db.Text)  # 页面文本内容
    content_html = db.Column(db.Text)  # 原始HTML内容
    
    # 页面元数据
    response_code = db.Column(db.Integer)  # HTTP响应码
    content_type = db.Column(db.String(100))
    content_length = db.Column(db.Integer)
    page_load_time = db.Column(db.Float)  # 页面加载时间（秒）
    
    # 提取的资源
    images_count = db.Column(db.Integer, default=0)
    links_count = db.Column(db.Integer, default=0)
    
    # 状态信息
    status = db.Column(db.String(20), default='pending')  # pending, processing, completed, failed
    is_nsfw = db.Column(db.Boolean, default=False)
    error_message = db.Column(db.Text)
    
    # 时间信息
    crawled_at = db.Column(db.DateTime, default=datetime.utcnow)
    processed_at = db.Column(db.DateTime)
    
    # 外键
    website_id = db.Column(db.Integer, db.ForeignKey('websites.id'), nullable=False)
    crawl_task_id = db.Column(db.Integer, db.ForeignKey('crawl_tasks.id'), nullable=False)
    
    # 关系
    detection_results = db.relationship('DetectionResult', backref='crawl_result', 
                                       lazy='dynamic', cascade='all, delete-orphan')
    images = db.relationship('PageImage', backref='crawl_result', 
                            lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, url, website_id, crawl_task_id, parent_url=None, depth=0):
        self.url = url
        self.website_id = website_id
        self.crawl_task_id = crawl_task_id
        self.parent_url = parent_url
        self.depth = depth
    
    def update_content(self, title=None, content_text=None, content_html=None, 
                      response_code=None, content_type=None, content_length=None, 
                      page_load_time=None):
        """更新页面内容"""
        if title:
            self.title = title
        if content_text:
            self.content_text = content_text
        if content_html:
            self.content_html = content_html
        if response_code:
            self.response_code = response_code
        if content_type:
            self.content_type = content_type
        if content_length:
            self.content_length = content_length
        if page_load_time:
            self.page_load_time = page_load_time
        
        self.status = 'processing'
        db.session.commit()
    
    def mark_completed(self, is_nsfw=False):
        """标记处理完成"""
        self.status = 'completed'
        self.is_nsfw = is_nsfw
        self.processed_at = datetime.utcnow()
        db.session.commit()
    
    def mark_failed(self, error_message):
        """标记处理失败"""
        self.status = 'failed'
        self.error_message = error_message
        self.processed_at = datetime.utcnow()
        db.session.commit()
    
    def get_detection_summary(self):
        """获取检测结果摘要"""
        total_detections = self.detection_results.count()
        nsfw_detections = self.detection_results.filter_by(is_nsfw=True).count()
        keyword_detections = self.detection_results.filter_by(detection_method='keyword').count()
        api_detections = self.detection_results.filter_by(detection_method='api').count()
        
        return {
            'total_detections': total_detections,
            'nsfw_detections': nsfw_detections,
            'keyword_detections': keyword_detections,
            'api_detections': api_detections
        }
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'url': self.url,
            'parent_url': self.parent_url,
            'depth': self.depth,
            'title': self.title,
            'content_length': self.content_length,
            'response_code': self.response_code,
            'content_type': self.content_type,
            'page_load_time': self.page_load_time,
            'images_count': self.images_count,
            'links_count': self.links_count,
            'status': self.status,
            'is_nsfw': self.is_nsfw,
            'error_message': self.error_message,
            'crawled_at': self.crawled_at.isoformat() if self.crawled_at else None,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'website_id': self.website_id,
            'crawl_task_id': self.crawl_task_id,
            'detection_summary': self.get_detection_summary()
        }
    
    def __repr__(self):
        return f'<CrawlResult {self.url}>' 