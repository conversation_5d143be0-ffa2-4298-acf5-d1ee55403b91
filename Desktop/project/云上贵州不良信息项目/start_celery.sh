#!/bin/bash
# Celery Worker启动脚本

echo "🚀 启动Celery Worker..."

# 切换到backend目录
cd "$(dirname "$0")/backend"

# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export FLASK_APP=app.py
export FLASK_ENV=development
export PYTHONPATH="$(pwd):$PYTHONPATH"

# 启动Celery Worker并将日志输出到文件
echo "📂 工作目录: $(pwd)"
echo "🔧 Python路径: $PYTHONPATH"

celery -A app.celery_app.celery worker --loglevel=info --concurrency=1 > ../logs/celery.log 2>&1 &

echo "✅ Celery Worker已启动，日志文件: ../logs/celery.log"