{"permissions": {"allow": ["Bash(npm install:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(yarn --version)", "Bash(npm:*)", "<PERSON><PERSON>(chmod:*)", "Bash(redis-cli:*)", "Bash(brew install:*)", "Bash(pip3 install:*)", "<PERSON>sh(redis-server:*)", "<PERSON><PERSON>(python3:*)", "Bash(\"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/redis_server/bin/redis-server\" --daemonize yes --port 6379)", "Bash(\"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/redis_server/bin/redis-cli\" ping)", "<PERSON><PERSON>(source:*)", "Bash(kill:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(pkill:*)", "Bash(./start_celery.sh)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(python:*)", "Bash(grep:*)", "Bash(./start_workers.sh:*)", "Bash(celery -A app.celery_app.celery inspect active)", "Bash(pip install:*)", "Bash(sqlite3:*)"], "deny": []}}